#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信通讯录管理窗口自动化激活脚本
功能：自动定位并激活微信应用程序窗口，导航到通讯录管理功能界面
作者：自动化脚本生成器
版本：1.0
"""

import pyautogui
import time
import logging
import sys
import os
from typing import Optional, Tuple, Dict, List, Any
import psutil
import win32gui
import win32con
import win32process
from datetime import datetime
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# 导入配置文件
try:
    from config import *
except ImportError:
    # 如果配置文件不存在，使用默认配置
    WECHAT_WINDOW_TITLES = ["微信", "WeChat", "微信 (WeChat)", "WeChat for Windows"]
    CONTACTS_WINDOW_TITLES = ["通讯录", "联系人", "contacts", "address book"]
    MAX_RETRIES = 3
    RETRY_DELAY = 2
    SHORT_DELAY = 1
    MEDIUM_DELAY = 2
    LONG_DELAY = 3
    PYAUTOGUI_PAUSE = 0.5
    PYAUTOGUI_FAILSAFE = True
    CONTACTS_BUTTON_X_RATIO = 0.0
    CONTACTS_BUTTON_Y_RATIO = 0.25
    CONTACTS_AREA_X_RATIO = 0.5
    CONTACTS_AREA_Y_RATIO = 0.5
    MANAGE_BUTTON_X_RATIO = 0.85
    MANAGE_BUTTON_Y_RATIO = 0.15
    MOUSE_MOVE_DURATION = 0.5
    LOG_LEVEL = "INFO"
    LOG_FILE = "wechat_automation.log"
    LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    PRIORITIZE_CONTACTS_WINDOWS = True
    CONTACTS_WINDOW_SCORE_BONUS = 500
    AUTO_DETECT_CONTACTS_INTERFACE = True

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class WeChatContactsAutomation:
    """微信通讯录管理自动化类"""
    
    def __init__(self):
        """初始化自动化配置"""
        # 设置pyautogui安全配置
        pyautogui.FAILSAFE = PYAUTOGUI_FAILSAFE
        pyautogui.PAUSE = PYAUTOGUI_PAUSE

        # 获取屏幕分辨率
        self.screen_width, self.screen_height = pyautogui.size()
        logger.info(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")

        # 微信窗口相关配置
        self.wechat_window_titles = WECHAT_WINDOW_TITLES

        # 重试配置
        self.max_retries = MAX_RETRIES
        self.retry_delay = RETRY_DELAY

        # 操作延时配置
        self.short_delay = SHORT_DELAY
        self.medium_delay = MEDIUM_DELAY
        self.long_delay = LONG_DELAY

        # 标签操作相关配置
        self.tag_keywords = getattr(globals(), 'TAG_RELATED_KEYWORDS', [
            "标签", "tag", "tags", "标记", "mark", "分类", "category"
        ])
        self.tag_operation_timeout = getattr(globals(), 'TAG_OPERATION_TIMEOUT', 15)
        self.tag_search_confidence = getattr(globals(), 'TAG_SEARCH_CONFIDENCE', 0.7)

        # 截图相关配置
        self.screenshot_dir = getattr(globals(), 'SCREENSHOT_DIR', 'screenshots')
        self.screenshot_format = getattr(globals(), 'SCREENSHOT_FORMAT', 'PNG')
        self.save_annotated_screenshots = getattr(globals(), 'SAVE_ANNOTATED_SCREENSHOTS', True)

        # 截图清理配置
        self.max_screenshots_to_keep = getattr(globals(), 'MAX_SCREENSHOTS_TO_KEEP', 5)
        self.auto_cleanup_screenshots = getattr(globals(), 'AUTO_CLEANUP_SCREENSHOTS', True)
        self.cleanup_on_startup = getattr(globals(), 'CLEANUP_ON_STARTUP', True)

        # 创建截图目录
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)

        # 启动时清理旧截图
        if self.cleanup_on_startup and self.auto_cleanup_screenshots:
            self.cleanup_old_screenshots()

        # 元素识别配置
        self.element_detection_confidence = getattr(globals(), 'ELEMENT_DETECTION_CONFIDENCE', 0.8)
        self.element_search_timeout = getattr(globals(), 'ELEMENT_SEARCH_TIMEOUT', 10)

        # 复合操作配置
        self.compound_operation_enabled = getattr(globals(), 'COMPOUND_OPERATION_ENABLED', True)
        self.tag_to_unswiped_delay = getattr(globals(), 'TAG_TO_UNSWIPED_DELAY', 3.0)
        self.unswiped_detection_timeout = getattr(globals(), 'UNSWIPED_DETECTION_TIMEOUT', 10)
        self.unswiped_click_confidence = getattr(globals(), 'UNSWIPED_CLICK_CONFIDENCE', 0.8)

        # "未刷"选项检测配置
        self.unswiped_keywords = getattr(globals(), 'UNSWIPED_KEYWORDS', ["未刷", "未读", "unread", "unswiped"])
        self.unswiped_detection_region_width = getattr(globals(), 'UNSWIPED_DETECTION_REGION_WIDTH', 400)
        self.unswiped_detection_region_height = getattr(globals(), 'UNSWIPED_DETECTION_REGION_HEIGHT', 200)
        self.unswiped_min_width = getattr(globals(), 'UNSWIPED_MIN_WIDTH', 30)
        self.unswiped_max_width = getattr(globals(), 'UNSWIPED_MAX_WIDTH', 120)
        self.unswiped_min_height = getattr(globals(), 'UNSWIPED_MIN_HEIGHT', 20)
        self.unswiped_max_height = getattr(globals(), 'UNSWIPED_MAX_HEIGHT', 50)

    def cleanup_old_screenshots(self) -> None:
        """
        清理旧的截图文件，只保留最新的几个文件
        """
        try:
            if not os.path.exists(self.screenshot_dir):
                logger.info("截图目录不存在，跳过清理")
                return

            # 获取所有截图文件
            screenshot_files = []
            for filename in os.listdir(self.screenshot_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    if os.path.isfile(filepath):
                        # 获取文件修改时间
                        mtime = os.path.getmtime(filepath)
                        screenshot_files.append((filepath, mtime, filename))

            if len(screenshot_files) <= self.max_screenshots_to_keep:
                logger.info(f"截图文件数量({len(screenshot_files)})未超过限制({self.max_screenshots_to_keep})，无需清理")
                return

            # 按修改时间排序，最新的在前
            screenshot_files.sort(key=lambda x: x[1], reverse=True)

            # 保留最新的文件，删除其余的
            files_to_keep = screenshot_files[:self.max_screenshots_to_keep]
            files_to_delete = screenshot_files[self.max_screenshots_to_keep:]

            logger.info(f"🧹 开始清理截图文件，保留最新的 {len(files_to_keep)} 个，删除 {len(files_to_delete)} 个旧文件")

            deleted_count = 0
            for filepath, mtime, filename in files_to_delete:
                try:
                    os.remove(filepath)
                    deleted_count += 1
                    logger.debug(f"已删除旧截图: {filename}")
                except Exception as e:
                    logger.warning(f"删除截图文件失败 {filename}: {e}")

            logger.info(f"✅ 截图清理完成，成功删除 {deleted_count} 个旧文件")

            # 记录保留的文件
            if files_to_keep:
                logger.info("📸 保留的最新截图文件:")
                for filepath, mtime, filename in files_to_keep:
                    timestamp = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                    logger.info(f"  - {filename} (修改时间: {timestamp})")

        except Exception as e:
            logger.error(f"清理截图文件时出错: {e}")

    def is_wechat_running(self) -> bool:
        """检查微信进程是否正在运行"""
        try:
            wechat_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    proc_name = proc.info['name']
                    if proc_name and 'wechat' in proc_name.lower():
                        # 更精确的微信进程识别，支持新版本微信
                        if proc_name.lower() in ['wechat.exe', 'wechatapp.exe', 'wechatappex.exe', 'weixin.exe']:
                            wechat_processes.append(proc.info)
                            logger.info(f"发现微信进程: {proc.info['name']} (PID: {proc.info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if wechat_processes:
                logger.info(f"共发现 {len(wechat_processes)} 个微信进程")
                return True
            else:
                logger.warning("未发现微信进程")
                return False

        except Exception as e:
            logger.error(f"检查微信进程时出错: {e}")
            return False

    def find_wechat_window(self) -> Optional[int]:
        """查找微信窗口句柄，优先查找通讯录管理相关窗口"""
        def enum_windows_callback(hwnd, windows):
            try:
                if not win32gui.IsWindowVisible(hwnd):
                    return True

                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 获取窗口进程信息
                _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                try:
                    process = psutil.Process(process_id)
                    process_name = process.name().lower()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    process_name = ""

                # 改进的微信窗口识别条件
                is_wechat_process = any(wechat_proc in process_name for wechat_proc in ['wechat', 'weixin'])
                is_main_window = any(title in window_title for title in self.wechat_window_titles)
                is_valid_class = class_name in ['WeChatMainWndForPC', 'ChatWnd', 'WeUIEngine', 'Qt51514QWindowIcon']

                # 检查是否是通讯录相关窗口
                is_contacts_window = self._is_contacts_related_window(window_title or "", class_name or "")

                # 检查窗口大小
                rect = win32gui.GetWindowRect(hwnd)
                window_width = rect[2] - rect[0]
                window_height = rect[3] - rect[1]

                # 放宽大小限制，因为微信可能处于最小化状态
                is_reasonable_size = window_width > 50 and window_height > 20

                # 综合判断是否为微信窗口（放宽条件）
                if is_wechat_process and (is_main_window or window_title.strip()) and is_reasonable_size:
                    windows.append({
                        'hwnd': hwnd,
                        'title': window_title,
                        'class_name': class_name,
                        'process_name': process_name,
                        'size': (window_width, window_height),
                        'rect': rect,
                        'is_valid_class': is_valid_class,
                        'is_main_window': is_main_window,
                        'is_contacts_window': is_contacts_window
                    })
                    logger.debug(f"候选微信窗口: {window_title} | 类名: {class_name} | 进程: {process_name} | 大小: {window_width}x{window_height} | 通讯录窗口: {is_contacts_window}")

            except Exception as e:
                logger.debug(f"枚举窗口时出错: {e}")

            return True

        windows = []
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)

            if not windows:
                logger.error("未找到符合条件的微信窗口")
                return None

            # 选择最合适的微信窗口（优先选择通讯录相关窗口）
            best_window = self._select_best_wechat_window(windows)
            if best_window:
                logger.info(f"选择微信窗口: {best_window['title']} (句柄: {best_window['hwnd']}, 类名: {best_window['class_name']}, 通讯录窗口: {best_window.get('is_contacts_window', False)})")
                return best_window['hwnd']
            else:
                logger.error("无法确定最佳的微信窗口")
                return None

        except Exception as e:
            logger.error(f"查找微信窗口时出错: {e}")
            return None

    def _is_contacts_related_window(self, window_title: str, class_name: str) -> bool:
        """检查窗口是否与通讯录管理相关（加强验证版）"""
        try:
            # 排除明显不是微信的窗口
            excluded_keywords = ["visual studio", "code", "program manager", "explorer"]
            title_lower = window_title.lower()

            for excluded in excluded_keywords:
                if excluded in title_lower:
                    logger.debug(f"排除非微信窗口: {window_title}")
                    return False

            # 只检测明确的微信通讯录关键词
            contacts_keywords = ["通讯录", "联系人"]

            # 检查窗口标题
            for keyword in contacts_keywords:
                if keyword in window_title:
                    logger.info(f"✅ 检测到微信通讯录相关窗口: {window_title} (关键词: {keyword})")
                    return True

            return False

        except Exception as e:
            logger.debug(f"检查通讯录相关窗口时出错: {e}")
            return False

    def _select_best_wechat_window(self, windows: list) -> Optional[dict]:
        """从候选窗口中选择最佳的微信窗口，使用精简的评分系统"""
        if not windows:
            return None

        # 改进的优先级评分系统
        def calculate_score(window):
            score = 0
            title = window['title']
            process_name = window['process_name']
            width, height = window['size']
            is_contacts_window = window.get('is_contacts_window', False)

            # 进程名匹配度评分（最重要的判断标准）
            if 'weixin.exe' in process_name:
                score += 200  # 官方微信进程
            elif 'wechat' in process_name.lower():
                score += 150
            else:
                # 非微信进程，直接返回0分
                return 0

            # 优先选择微信主窗口，而不是可能误识别的通讯录窗口
            if title == "微信":
                score += 300  # 微信主窗口最高优先级
            elif "微信" in title and "通讯录" not in title:
                score += 250  # 其他微信窗口
            elif is_contacts_window and "通讯录" in title:
                score += 200  # 真正的通讯录窗口
            elif "WeChat" in title:
                score += 180

            # 窗口大小评分（简化）
            if width >= 400 and height >= 300:
                score += 30  # 正常大小窗口
            elif width >= 100 and height >= 50:
                score += 10  # 可能是最小化状态
            else:
                score -= 20  # 过小的窗口

            return score

        # 计算评分并选择最佳窗口
        best_window = None
        best_score = 0

        for window in windows:
            score = calculate_score(window)
            is_contacts = window.get('is_contacts_window', False)
            logger.debug(f"窗口评分: {window['title']} -> {score}分 (通讯录: {is_contacts})")

            if score > best_score:
                best_score = score
                best_window = window

        if best_window and best_score > 0:
            is_contacts = best_window.get('is_contacts_window', False)
            logger.info(f"✅ 选择最佳窗口: {best_window['title']} (评分: {best_score}, 通讯录窗口: {is_contacts})")
            return best_window

        logger.warning("所有候选窗口评分都过低，可能不是微信窗口")
        return None

    def verify_wechat_window(self, hwnd: int) -> bool:
        """验证窗口是否确实是微信窗口（包括通讯录管理窗口）"""
        try:
            # 检查窗口是否仍然存在和可见
            if not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd):
                logger.error("窗口不存在或不可见")
                return False

            # 获取窗口信息
            window_title = win32gui.GetWindowText(hwnd)

            # 获取进程信息
            _, process_id = win32process.GetWindowThreadProcessId(hwnd)
            try:
                process = psutil.Process(process_id)
                process_name = process.name().lower()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                logger.error("无法获取窗口进程信息")
                return False

            # 验证是否为微信进程（精简版）
            if 'wechat' not in process_name and 'weixin' not in process_name:
                logger.error(f"窗口不属于微信进程: {process_name}")
                return False

            # 检查是否是通讯录相关窗口
            is_contacts_window = self._is_contacts_related_window(window_title or "", "")

            # 简化的标题验证
            is_valid_title = (
                "微信" in window_title or
                "WeChat" in window_title or
                is_contacts_window or
                window_title.strip()  # 允许有标题的窗口
            )

            if not is_valid_title:
                logger.error(f"窗口标题不匹配: {window_title}")
                return False

            logger.info(f"✅ 窗口验证通过: {window_title} (进程: {process_name})")

            return True

        except Exception as e:
            logger.error(f"验证微信窗口时出错: {e}")
            return False

    def activate_window(self, hwnd: int) -> bool:
        """激活指定窗口"""
        try:
            # 首先验证窗口
            if not self.verify_wechat_window(hwnd):
                logger.error("窗口验证失败，停止激活操作")
                return False

            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                logger.info("微信窗口已最小化，正在还原...")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(self.short_delay)

            # 确保窗口正常显示
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            time.sleep(self.short_delay)

            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(self.short_delay)

            # 验证窗口是否成功激活
            active_hwnd = win32gui.GetForegroundWindow()
            if active_hwnd == hwnd:
                logger.info("微信窗口激活成功")
                # 再次验证激活的窗口确实是微信
                return self.verify_wechat_window(active_hwnd)
            else:
                active_title = win32gui.GetWindowText(active_hwnd) if active_hwnd else "未知"
                logger.error(f"微信窗口激活失败，当前前台窗口: {active_title} (句柄: {active_hwnd})")
                return False

        except Exception as e:
            logger.error(f"激活微信窗口时出错: {e}")
            return False

    def get_window_rect(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """获取窗口位置和大小"""
        try:
            rect = win32gui.GetWindowRect(hwnd)
            logger.info(f"微信窗口位置: left={rect[0]}, top={rect[1]}, right={rect[2]}, bottom={rect[3]}")
            return rect
        except Exception as e:
            logger.error(f"获取窗口位置时出错: {e}")
            return None

    def click_contacts_button(self, window_rect: Tuple[int, int, int, int]) -> bool:
        """点击通讯录按钮"""
        try:
            left, top, right, bottom = window_rect
            window_width = right - left
            window_height = bottom - top

            logger.info(f"微信窗口尺寸: {window_width}x{window_height}")

            # 改进的通讯录按钮定位算法
            # 微信左侧导航栏通常宽度约为60-80像素
            nav_bar_width = min(80, window_width * 0.08)

            # 通讯录按钮通常在导航栏的第二个位置
            # 从顶部开始，跳过聊天按钮，定位到通讯录按钮
            button_height = 50  # 每个按钮大约50像素高
            title_bar_height = 30  # 标题栏高度

            # 计算通讯录按钮的位置
            contacts_x = left + int(nav_bar_width / 2)  # 导航栏中央
            contacts_y = top + title_bar_height + button_height + int(button_height / 2)  # 第二个按钮的中央

            # 确保坐标在窗口范围内
            contacts_x = max(left + 10, min(contacts_x, left + nav_bar_width - 10))
            contacts_y = max(top + 50, min(contacts_y, bottom - 50))

            logger.info(f"计算的通讯录按钮坐标: ({contacts_x}, {contacts_y})")
            logger.info(f"导航栏宽度: {nav_bar_width}, 按钮高度: {button_height}")

            # 尝试多个可能的位置
            possible_positions = [
                (contacts_x, contacts_y),  # 主要位置
                (left + 40, top + 120),    # 备选位置1
                (left + 50, top + 150),    # 备选位置2
                (left + 35, top + 100),    # 备选位置3
            ]

            for i, (x, y) in enumerate(possible_positions):
                # 确保坐标在窗口范围内
                x = max(left + 10, min(x, right - 10))
                y = max(top + 50, min(y, bottom - 50))

                logger.info(f"尝试位置 {i+1}: ({x}, {y})")

                # 移动鼠标到目标位置
                pyautogui.moveTo(x, y, duration=MOUSE_MOVE_DURATION)
                time.sleep(self.short_delay)

                # 执行点击
                pyautogui.click(x, y)
                time.sleep(self.medium_delay)

                # 检查是否成功进入通讯录界面
                if self._verify_contacts_interface():
                    logger.info(f"通讯录按钮点击成功，位置: ({x}, {y})")
                    return True
                else:
                    logger.warning(f"位置 {i+1} 点击后未进入通讯录界面，尝试下一个位置")

            logger.error("所有位置尝试完毕，未能成功点击通讯录按钮")
            return False

        except Exception as e:
            logger.error(f"点击通讯录按钮时出错: {e}")
            return False

    def _verify_contacts_interface(self) -> bool:
        """验证是否成功进入通讯录界面"""
        try:
            # 等待界面加载
            time.sleep(1)

            # 获取当前前台窗口
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                return False

            # 验证仍然是微信窗口
            if not self.verify_wechat_window(hwnd):
                return False

            # 这里可以添加更多的界面验证逻辑
            # 比如检查特定的UI元素、窗口标题变化等
            # 由于微信界面可能因版本而异，这里使用简单的验证

            logger.info("通讯录界面验证通过")
            return True

        except Exception as e:
            logger.debug(f"验证通讯录界面时出错: {e}")
            return False

    def click_manage_contacts(self, window_rect: Tuple[int, int, int, int]) -> bool:
        """点击通讯录管理选项"""
        try:
            left, top, right, bottom = window_rect
            window_width = right - left
            window_height = bottom - top

            logger.info("开始查找通讯录管理选项...")

            # 方法1: 查找右上角的管理按钮或菜单按钮
            possible_manage_positions = [
                # 右上角区域的可能位置
                (right - 100, top + 80),   # 右上角管理按钮
                (right - 150, top + 60),   # 稍微左一点的位置
                (right - 80, top + 100),   # 稍微下一点的位置
                (right - 50, top + 120),   # 更靠右的位置
            ]

            for i, (x, y) in enumerate(possible_manage_positions):
                # 确保坐标在窗口范围内
                x = max(left + 100, min(x, right - 20))
                y = max(top + 50, min(y, bottom - 50))

                logger.info(f"尝试管理按钮位置 {i+1}: ({x}, {y})")

                pyautogui.moveTo(x, y, duration=MOUSE_MOVE_DURATION)
                time.sleep(self.short_delay)
                pyautogui.click(x, y)
                time.sleep(self.medium_delay)

                # 检查是否出现了管理相关的界面或菜单
                if self._check_for_management_interface():
                    logger.info(f"成功找到管理选项，位置: ({x}, {y})")
                    return True

            # 方法2: 尝试右键点击通讯录列表区域
            contacts_list_positions = [
                (left + window_width * 0.3, top + window_height * 0.4),  # 通讯录列表中央
                (left + window_width * 0.25, top + window_height * 0.3), # 列表上方
                (left + window_width * 0.35, top + window_height * 0.5), # 列表下方
            ]

            for i, (x, y) in enumerate(contacts_list_positions):
                x = int(max(left + 100, min(x, right - 100)))
                y = int(max(top + 100, min(y, bottom - 100)))

                logger.info(f"尝试右键点击通讯录列表位置 {i+1}: ({x}, {y})")

                pyautogui.moveTo(x, y, duration=MOUSE_MOVE_DURATION)
                time.sleep(self.short_delay)
                pyautogui.rightClick(x, y)
                time.sleep(self.medium_delay)

                # 检查是否出现了上下文菜单
                if self._check_for_context_menu():
                    logger.info(f"成功打开上下文菜单，位置: ({x}, {y})")
                    return True

            # 方法3: 尝试使用键盘快捷键
            logger.info("尝试使用键盘快捷键...")
            pyautogui.hotkey('ctrl', 'shift', 'm')  # 假设的管理快捷键
            time.sleep(self.medium_delay)

            if self._check_for_management_interface():
                logger.info("键盘快捷键成功打开管理界面")
                return True

            logger.warning("所有方法都未能成功打开通讯录管理界面")
            return False

        except Exception as e:
            logger.error(f"点击通讯录管理时出错: {e}")
            return False

    def _check_for_management_interface(self) -> bool:
        """检查是否出现了管理界面"""
        try:
            # 等待界面响应
            time.sleep(0.5)

            # 获取当前前台窗口
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                return False

            window_title = win32gui.GetWindowText(hwnd)

            # 检查窗口标题是否包含管理相关的关键词
            management_keywords = ["管理", "设置", "编辑", "manage", "setting", "edit"]
            for keyword in management_keywords:
                if keyword in window_title.lower():
                    logger.info(f"检测到管理界面: {window_title}")
                    return True

            # 这里可以添加更多的界面检测逻辑
            # 比如检查特定的UI元素等

            return False

        except Exception as e:
            logger.debug(f"检查管理界面时出错: {e}")
            return False

    def _check_for_context_menu(self) -> bool:
        """检查是否出现了上下文菜单"""
        try:
            # 等待菜单出现
            time.sleep(0.5)

            # 简单的菜单检测逻辑
            # 在实际应用中，可以通过检查特定的窗口类名或UI元素来判断
            # 这里使用简化的检测方法

            # 尝试检测是否有新的小窗口出现（可能是上下文菜单）
            def enum_windows_callback(hwnd, menus):
                if win32gui.IsWindowVisible(hwnd):
                    class_name = win32gui.GetClassName(hwnd)
                    window_title = win32gui.GetWindowText(hwnd)

                    # 检查是否是菜单类型的窗口
                    if class_name and any(menu_class in class_name.lower() for menu_class in ['menu', 'popup', 'context']):
                        menus.append((hwnd, class_name, window_title))

                return True

            menus = []
            win32gui.EnumWindows(enum_windows_callback, menus)

            if menus:
                logger.info(f"检测到可能的上下文菜单: {len(menus)} 个")
                return True

            return False

        except Exception as e:
            logger.debug(f"检查上下文菜单时出错: {e}")
            return False

    def check_if_already_in_contacts(self, hwnd: int) -> bool:
        """检查当前是否已经在通讯录界面"""
        try:
            # 获取窗口信息
            window_title = win32gui.GetWindowText(hwnd)

            # 检查窗口标题是否包含通讯录相关信息
            if self._is_contacts_related_window(window_title, ""):
                logger.info("✅ 检测到当前已在通讯录相关界面")
                return True

            # 这里可以添加更多的界面检测逻辑
            # 比如检查特定的UI元素、控件等

            return False

        except Exception as e:
            logger.debug(f"检查通讯录界面时出错: {e}")
            return False

    def execute_automation(self) -> bool:
        """执行完整的自动化流程"""
        logger.info("开始执行微信通讯录管理自动化流程")

        # 步骤1: 检查微信是否运行
        if not self.is_wechat_running():
            logger.error("微信未运行，请先启动微信应用程序")
            return False

        # 步骤2: 查找微信窗口（优先查找通讯录相关窗口）
        hwnd = self.find_wechat_window()
        if not hwnd:
            logger.error("无法找到微信窗口")
            return False

        # 步骤3: 激活微信窗口
        if not self.activate_window(hwnd):
            logger.error("无法激活微信窗口")
            return False

        # 步骤4: 检查是否已经在通讯录界面
        if self.check_if_already_in_contacts(hwnd):
            logger.info("✅ 当前已在通讯录界面，无需进一步导航")
            return True

        # 步骤5: 获取窗口位置信息
        window_rect = self.get_window_rect(hwnd)
        if not window_rect:
            logger.error("无法获取微信窗口位置信息")
            return False

        # 步骤6: 点击通讯录按钮
        for attempt in range(self.max_retries):
            logger.info(f"尝试点击通讯录按钮 (第{attempt + 1}次)")
            if self.click_contacts_button(window_rect):
                break
            if attempt < self.max_retries - 1:
                logger.warning(f"点击失败，{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        else:
            logger.error("多次尝试点击通讯录按钮均失败")
            return False

        # 步骤7: 点击通讯录管理
        for attempt in range(self.max_retries):
            logger.info(f"尝试访问通讯录管理 (第{attempt + 1}次)")
            if self.click_manage_contacts(window_rect):
                break
            if attempt < self.max_retries - 1:
                logger.warning(f"操作失败，{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        else:
            logger.error("多次尝试访问通讯录管理均失败")
            return False

        logger.info("✅ 微信通讯录管理自动化流程执行完成")
        return True

    # ==================== 标签操作功能模块 ====================

    def capture_window_screenshot(self, hwnd: int, save_path: Optional[str] = None,
                                 skip_activation: bool = False) -> Optional[str]:
        """
        捕获指定窗口的截图

        Args:
            hwnd: 窗口句柄
            save_path: 保存路径，如果为None则自动生成
            skip_activation: 是否跳过窗口激活

        Returns:
            截图文件路径，失败返回None
        """
        try:
            # 验证窗口
            if not self.verify_wechat_window(hwnd):
                logger.error("窗口验证失败，无法截图")
                return None

            # 获取窗口位置和大小
            window_rect = self.get_window_rect(hwnd)
            if not window_rect:
                logger.error("无法获取窗口位置信息")
                return None

            left, top, right, bottom = window_rect
            width = right - left
            height = bottom - top

            logger.info(f"准备截图窗口: 位置({left}, {top}), 大小({width}x{height})")

            # 只在需要时激活窗口
            if not skip_activation:
                if not self.activate_window(hwnd):
                    logger.warning("窗口激活失败，但继续尝试截图")
                time.sleep(self.short_delay)
            else:
                logger.debug("跳过窗口激活，直接截图")

            # 使用pyautogui截图指定区域
            screenshot = pyautogui.screenshot(region=(left, top, width, height))

            # 生成保存路径
            if save_path is None:
                timestamp = datetime.now().strftime(getattr(globals(), 'SCREENSHOT_TIMESTAMP_FORMAT', '%Y%m%d_%H%M%S'))
                filename = f"wechat_window_{timestamp}.{self.screenshot_format.lower()}"
                save_path = os.path.join(self.screenshot_dir, filename)

            # 保存截图
            screenshot.save(save_path, format=self.screenshot_format)
            logger.info(f"窗口截图已保存: {save_path}")

            return save_path

        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None

    def analyze_window_elements(self, hwnd: int, screenshot_path: Optional[str] = None) -> Dict[str, Any]:
        """
        分析窗口中的可交互元素

        Args:
            hwnd: 窗口句柄
            screenshot_path: 截图路径，如果为None则重新截图

        Returns:
            包含元素信息的字典
        """
        try:
            # 获取或创建截图
            if screenshot_path is None:
                screenshot_path = self.capture_window_screenshot(hwnd)
                if not screenshot_path:
                    logger.error("无法获取窗口截图")
                    return {}

            # 获取窗口信息
            window_rect = self.get_window_rect(hwnd)
            if not window_rect:
                logger.error("无法获取窗口位置信息")
                return {}

            left, top, right, bottom = window_rect
            window_info = {
                'hwnd': hwnd,
                'rect': window_rect,
                'width': right - left,
                'height': bottom - top,
                'screenshot_path': screenshot_path
            }

            logger.info(f"开始分析窗口元素: {window_info['width']}x{window_info['height']}")

            # 加载截图进行分析
            image = cv2.imread(screenshot_path)
            if image is None:
                logger.error(f"无法加载截图文件: {screenshot_path}")
                return window_info

            # 优先分析左侧标签区域（0-200像素）
            left_sidebar_elements = self.analyze_left_sidebar_elements(image, window_info)

            # 如果左侧区域检测到元素，优先使用这些元素
            if left_sidebar_elements:
                logger.info(f"✅ 左侧标签区域检测到 {len(left_sidebar_elements)} 个元素")
                window_info['elements'] = left_sidebar_elements
                window_info['detection_method'] = 'left_sidebar_focused'
            else:
                # 如果左侧区域没有检测到元素，使用全窗口分析作为备选
                logger.warning("左侧区域未检测到元素，使用全窗口分析作为备选")
                window_info['elements'] = self._analyze_full_window_elements(image, window_info)
                window_info['detection_method'] = 'full_window_fallback'

            # 保存带注释的截图
            if self.save_annotated_screenshots:
                self._save_annotated_screenshot(image, window_info['elements'], screenshot_path)

            return window_info

        except Exception as e:
            logger.error(f"元素分析失败: {e}")
            return {'hwnd': hwnd, 'elements': []}

    def analyze_left_sidebar_elements(self, image: np.ndarray, window_info: Dict) -> List[Dict]:
        """
        专门分析窗口左侧200像素区域的标签元素，增强颜色特征检测

        Args:
            image: OpenCV图像数组
            window_info: 窗口信息

        Returns:
            左侧区域检测到的元素列表
        """
        try:
            logger.info("🎯 开始专门分析左侧标签区域 (0-200像素)")

            # 定义左侧分析区域
            LEFT_SIDEBAR_WIDTH = 200
            window_height = window_info['height']
            window_width = window_info['width']

            # 确保不超出窗口边界
            sidebar_width = min(LEFT_SIDEBAR_WIDTH, window_width)

            # 提取左侧区域
            left_region = image[:window_height, :sidebar_width]
            logger.info(f"左侧分析区域: 宽度={sidebar_width}, 高度={window_height}")

            # 转换为不同颜色空间进行分析
            gray_left = cv2.cvtColor(left_region, cv2.COLOR_BGR2GRAY)
            hsv_left = cv2.cvtColor(left_region, cv2.COLOR_BGR2HSV)

            # 检测黄色区域（标签通常有黄色背景）
            yellow_elements = self._detect_yellow_regions(left_region, hsv_left)

            # 专门检测"标签"文本区域
            tag_text_elements = self._detect_tag_text_regions(left_region, window_info)

            # 使用多种轮廓检测方法
            all_contours = []

            # 方法1: Canny边缘检测 - 针对左侧区域优化参数
            edges = cv2.Canny(gray_left, 20, 80, apertureSize=3)
            contours_canny, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours.extend(contours_canny)

            # 方法2: 自适应阈值检测
            adaptive_thresh = cv2.adaptiveThreshold(gray_left, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 9, 2)
            contours_adaptive, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours.extend(contours_adaptive)

            # 方法3: 形态学梯度检测
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            morph_grad = cv2.morphologyEx(gray_left, cv2.MORPH_GRADIENT, kernel)
            _, morph_thresh = cv2.threshold(morph_grad, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            contours_morph, _ = cv2.findContours(morph_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours.extend(contours_morph)

            logger.info(f"轮廓检测统计: Canny={len(contours_canny)}, 自适应={len(contours_adaptive)}, 形态学={len(contours_morph)}, 黄色区域={len(yellow_elements)}, 文本区域={len(tag_text_elements)}")

            # 优先处理文本检测结果
            elements = []
            processed_areas = set()

            # 首先添加文本检测的结果（最高优先级）
            for text_element in tag_text_elements:
                elements.append(text_element)
                x, y, w, h = text_element['rect']
                # 标记这个区域已处理，避免重复
                area_key = (x//5, y//5, w//5, h//5)
                processed_areas.add(area_key)
                logger.info(f"🏷️ 添加文本检测结果: 类型={text_element['type']}, "
                           f"位置=({x},{y}), 置信度={text_element['confidence']:.2f}")

            # 然后处理轮廓检测结果

            for contour in all_contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 避免重复检测相同区域
                area_key = (x//5, y//5, w//5, h//5)  # 5像素精度去重
                if area_key in processed_areas:
                    continue
                processed_areas.add(area_key)

                # 过滤条件：专门针对标签元素
                if (w < 8 or h < 8 or  # 太小的元素
                    w > sidebar_width * 0.9 or h > window_height * 0.8 or  # 太大的元素
                    x + w > sidebar_width):  # 超出左侧区域的元素
                    continue

                # 计算几何属性
                area = w * h
                aspect_ratio = w / h if h > 0 else 0

                # 检查是否在黄色区域内
                has_yellow_background = self._check_yellow_background(x, y, w, h, yellow_elements)

                # 标签元素识别逻辑（更严格的条件）
                element_type = "unknown"
                confidence = 0.1
                is_likely_tag = False

                # 优先识别有黄色背景的元素（真正的标签）
                if has_yellow_background:
                    if (20 <= w <= 180 and 15 <= h <= 50 and 1.5 <= aspect_ratio <= 8.0):
                        element_type = "tag_button"
                        confidence = 0.95
                        is_likely_tag = True
                        logger.info(f"🏷️ 发现黄色背景标签: 位置=({x},{y}), 大小=({w}x{h})")

                # 标签按钮特征 (较大的矩形按钮)
                elif (30 <= w <= 180 and 25 <= h <= 60 and
                      1.5 <= aspect_ratio <= 6.0):
                    element_type = "tag_button"
                    confidence = 0.8
                    is_likely_tag = True

                # 标签图标特征
                elif (15 <= w <= 45 and 15 <= h <= 45 and
                      0.7 <= aspect_ratio <= 1.4):
                    element_type = "tag_icon"
                    confidence = 0.7
                    is_likely_tag = True

                # 降低对小文本的识别（减少噪音）
                elif (20 <= w <= 120 and 12 <= h <= 25 and
                      aspect_ratio >= 2.0 and area >= 300):
                    element_type = "tag_text"
                    confidence = 0.4  # 降低置信度

                # 只保留可能的标签元素
                if confidence < 0.5 and not is_likely_tag:
                    continue

                # 计算屏幕绝对坐标
                window_rect = window_info['rect']
                abs_x = window_rect[0] + x
                abs_y = window_rect[1] + y

                element = {
                    'id': len(elements),
                    'type': element_type,
                    'rect': (x, y, w, h),
                    'center': (x + w//2, y + h//2),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'confidence': confidence,
                    'absolute_pos': (abs_x, abs_y),
                    'absolute_center': (abs_x + w//2, abs_y + h//2),
                    'detection_region': 'left_sidebar',
                    'has_yellow_background': has_yellow_background,
                    'is_likely_tag': is_likely_tag
                }
                elements.append(element)

                logger.info(f"检测到左侧元素 {len(elements)}: 类型={element_type}, "
                           f"位置=({x},{y}), 大小=({w}x{h}), "
                           f"屏幕坐标=({abs_x},{abs_y}), 置信度={confidence:.2f}, "
                           f"黄色背景={has_yellow_background}")

            # 按优先级排序：黄色背景 > 高置信度 > 位置
            elements.sort(key=lambda e: (
                e.get('has_yellow_background', False),
                e.get('is_likely_tag', False),
                e['confidence'],
                -e['rect'][1]  # Y坐标越小越优先
            ), reverse=True)

            # 只保留高质量的候选元素
            filtered_elements = [e for e in elements if e['confidence'] >= 0.5 or e.get('is_likely_tag', False)]

            logger.info(f"✅ 左侧区域分析完成，原始检测: {len(elements)} 个，过滤后: {len(filtered_elements)} 个高质量标签元素")

            # 保存左侧区域的调试截图
            if self.save_annotated_screenshots:
                self._save_left_sidebar_debug_screenshot(left_region, filtered_elements, window_info)

            return filtered_elements

        except Exception as e:
            logger.error(f"左侧区域分析失败: {e}")
            return []

    def _detect_yellow_regions(self, bgr_image: np.ndarray, hsv_image: np.ndarray) -> List[Dict]:
        """
        检测图像中的黄色区域（标签背景）

        Args:
            bgr_image: BGR格式图像
            hsv_image: HSV格式图像

        Returns:
            黄色区域列表
        """
        try:
            # 定义黄色的HSV范围
            # 黄色在HSV中的范围：H(20-30), S(100-255), V(100-255)
            lower_yellow = np.array([15, 80, 80])
            upper_yellow = np.array([35, 255, 255])

            # 创建黄色掩码
            yellow_mask = cv2.inRange(hsv_image, lower_yellow, upper_yellow)

            # 形态学操作去除噪点
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            yellow_mask = cv2.morphologyEx(yellow_mask, cv2.MORPH_CLOSE, kernel)
            yellow_mask = cv2.morphologyEx(yellow_mask, cv2.MORPH_OPEN, kernel)

            # 查找黄色区域的轮廓
            contours, _ = cv2.findContours(yellow_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            yellow_regions = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # 过滤太小的区域
                if area >= 100:  # 至少100像素的区域
                    yellow_regions.append({
                        'rect': (x, y, w, h),
                        'area': area,
                        'center': (x + w//2, y + h//2)
                    })
                    logger.info(f"🟡 检测到黄色区域: 位置=({x},{y}), 大小=({w}x{h}), 面积={area}")

            return yellow_regions

        except Exception as e:
            logger.error(f"黄色区域检测失败: {e}")
            return []

    def _check_yellow_background(self, x: int, y: int, w: int, h: int, yellow_regions: List[Dict]) -> bool:
        """
        检查指定区域是否有黄色背景

        Args:
            x, y, w, h: 要检查的矩形区域
            yellow_regions: 检测到的黄色区域列表

        Returns:
            是否有黄色背景
        """
        try:
            element_rect = (x, y, w, h)
            element_center = (x + w//2, y + h//2)

            for yellow_region in yellow_regions:
                yellow_x, yellow_y, yellow_w, yellow_h = yellow_region['rect']

                # 检查元素是否在黄色区域内或重叠
                overlap_x = max(0, min(x + w, yellow_x + yellow_w) - max(x, yellow_x))
                overlap_y = max(0, min(y + h, yellow_y + yellow_h) - max(y, yellow_y))
                overlap_area = overlap_x * overlap_y

                # 如果重叠面积超过元素面积的50%，认为有黄色背景
                element_area = w * h
                if overlap_area >= element_area * 0.5:
                    return True

                # 或者元素中心在黄色区域内
                if (yellow_x <= element_center[0] <= yellow_x + yellow_w and
                    yellow_y <= element_center[1] <= yellow_y + yellow_h):
                    return True

            return False

        except Exception as e:
            logger.error(f"黄色背景检查失败: {e}")
            return False

    def _detect_tag_text_regions(self, left_region: np.ndarray, window_info: Dict) -> List[Dict]:
        """
        专门检测"标签"文本区域

        Args:
            left_region: 左侧区域图像
            window_info: 窗口信息

        Returns:
            检测到的标签文本区域列表
        """
        try:
            logger.info("🔍 开始专门检测'标签'文本区域")

            # 转换为灰度图像
            gray = cv2.cvtColor(left_region, cv2.COLOR_BGR2GRAY)

            # 使用模板匹配检测"标签"文本的可能位置
            # 基于微信界面的特征，标签通常在左侧菜单中
            height, width = left_region.shape[:2]

            tag_regions = []

            # 方法1: 简化的位置特征检测
            # 只检测最可能的标签位置，减少噪音
            logger.info("🎯 使用简化的标签位置检测，减少噪音")

            # 方法2: 基于已知的"标签"菜单项位置
            # 根据您提供的截图，标签位置应该在更下方
            known_tag_positions = [
                (15, 175, 40, 20),   # 根据截图调整的位置
                (15, 180, 40, 20),   # 稍微下方
                (15, 185, 40, 20),   # 更下方
                (20, 175, 35, 18),   # 稍微右移
                (20, 180, 35, 18),   # 稍微右移下方
            ]

            for x, y, w, h in known_tag_positions:
                if x + w < width and y + h < height:
                    # 计算屏幕绝对坐标
                    window_rect = window_info['rect']
                    abs_x = window_rect[0] + x
                    abs_y = window_rect[1] + y

                    tag_region = {
                        'rect': (x, y, w, h),
                        'center': (x + w//2, y + h//2),
                        'area': w * h,
                        'confidence': 0.8,
                        'type': 'tag_text_known_position',
                        'absolute_pos': (abs_x, abs_y),
                        'absolute_center': (abs_x + w//2, abs_y + h//2),
                        'detection_method': 'known_position',
                        'is_likely_tag': True
                    }
                    tag_regions.append(tag_region)

                    logger.info(f"📍 添加已知标签位置: 位置=({x},{y}), 大小=({w}x{h})")

            # 按置信度排序
            tag_regions.sort(key=lambda x: x['confidence'], reverse=True)

            logger.info(f"✅ 标签文本检测完成，共找到 {len(tag_regions)} 个候选区域")

            return tag_regions

        except Exception as e:
            logger.error(f"标签文本检测失败: {e}")
            return []

    def _analyze_full_window_elements(self, image: np.ndarray, window_info: Dict) -> List[Dict]:
        """
        分析整个窗口的元素（备选方法）

        Args:
            image: OpenCV图像数组
            window_info: 窗口信息

        Returns:
            检测到的元素列表
        """
        try:
            logger.info("使用全窗口分析作为备选方法")

            # 转换为灰度图像进行边缘检测
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 使用多种方法检测UI元素，提高检测准确性
            # 方法1: Canny边缘检测（原有方法）
            edges = cv2.Canny(gray, 30, 100, apertureSize=3)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 方法2: 自适应阈值检测
            adaptive_thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            adaptive_contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 方法3: 形态学操作检测
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            morph = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)
            morph_thresh = cv2.threshold(morph, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
            morph_contours, _ = cv2.findContours(morph_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 合并所有检测到的轮廓
            all_contours = list(contours) + list(adaptive_contours) + list(morph_contours)
            logger.info(f"检测方法统计: Canny={len(contours)}, 自适应={len(adaptive_contours)}, 形态学={len(morph_contours)}")

            # 分析轮廓，识别可能的按钮和交互元素
            elements = []
            processed_areas = set()  # 避免重复检测相同区域
            left, top = window_info['rect'][:2]

            for i, contour in enumerate(all_contours):
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 创建区域标识符，避免重复
                area_key = (x//10, y//10, w//10, h//10)  # 10像素精度去重
                if area_key in processed_areas:
                    continue
                processed_areas.add(area_key)

                # 放宽过滤条件，提高检测率
                if w < 10 or h < 10 or w > window_info['width'] * 0.9 or h > window_info['height'] * 0.9:
                    continue

                # 计算面积和长宽比
                area = w * h
                aspect_ratio = w / h if h > 0 else 0

                # 更灵活的元素类型判断
                element_type = "unknown"
                confidence = 0.5

                if 1.2 <= aspect_ratio <= 8 and 15 <= h <= 60 and 30 <= w <= 300:
                    element_type = "button"
                    confidence = 0.8
                elif 0.6 <= aspect_ratio <= 1.4 and 10 <= w <= 50 and 10 <= h <= 50:
                    element_type = "icon"
                    confidence = 0.7
                elif aspect_ratio > 3 and h <= 40 and w >= 50:
                    element_type = "text_field"
                    confidence = 0.6
                elif w > 80 and h > 80:
                    element_type = "panel"
                    confidence = 0.4
                elif 20 <= w <= 200 and 15 <= h <= 80:  # 通用可点击元素
                    element_type = "clickable"
                    confidence = 0.6

                element = {
                    'id': len(elements),
                    'type': element_type,
                    'rect': (x, y, w, h),
                    'center': (x + w//2, y + h//2),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'confidence': confidence,
                    'absolute_pos': (left + x, top + y),  # 屏幕绝对坐标
                    'detection_region': 'full_window'
                }
                elements.append(element)

            logger.info(f"全窗口分析检测到 {len(elements)} 个可能的UI元素")

            # 详细记录检测结果用于调试
            if len(elements) == 0:
                logger.warning("⚠️ 未检测到任何UI元素，可能的原因:")
                logger.warning("   1. 窗口内容过于简单或单一颜色")
                logger.warning("   2. 界面元素边缘不够清晰")
                logger.warning("   3. 检测参数需要调整")
                logger.warning("   4. 当前不在正确的界面")

                # 保存调试信息
                logger.info(f"窗口信息: 大小={window_info['width']}x{window_info['height']}")

                # 尝试保存原始图像的统计信息
                try:
                    gray_array = np.array(gray, dtype=np.float32)
                    mean_val = np.mean(gray_array)
                    std_val = np.std(gray_array)
                    logger.info(f"图像统计: 平均值={mean_val:.2f}, 标准差={std_val:.2f}")
                    if std_val < 10:
                        logger.warning("   图像标准差过低，可能是单一颜色背景")
                except Exception as e:
                    logger.debug(f"图像统计分析失败: {e}")
            else:
                # 记录检测到的元素类型统计
                type_counts = {}
                for element in elements:
                    elem_type = element['type']
                    type_counts[elem_type] = type_counts.get(elem_type, 0) + 1

                logger.info(f"元素类型统计: {type_counts}")

            return elements

        except Exception as e:
            logger.error(f"全窗口元素分析失败: {e}")
            return []

    def _save_left_sidebar_debug_screenshot(self, left_region: np.ndarray, elements: List[Dict], window_info: Dict) -> None:
        """
        保存左侧区域的调试截图，只标注高质量的标签元素

        Args:
            left_region: 左侧区域图像
            elements: 检测到的元素列表
            window_info: 窗口信息
        """
        try:
            # 创建调试图像副本
            debug_image = left_region.copy()

            # 定义颜色方案（更清晰的标注）
            colors = {
                'tag_button': (0, 255, 0),      # 绿色 - 标签按钮（最重要）
                'tag_icon': (255, 0, 0),        # 蓝色 - 标签图标
                'tag_text': (0, 255, 255),      # 黄色 - 标签文本
                'clickable': (255, 0, 255),     # 紫色 - 可点击元素
                'unknown': (128, 128, 128)       # 灰色 - 未知元素
            }

            # 特殊标记颜色
            yellow_bg_color = (0, 255, 255)    # 青色 - 黄色背景元素
            high_confidence_color = (0, 255, 0) # 绿色 - 高置信度元素

            logger.info("🎨 开始绘制优化的左侧区域调试标注...")

            # 只绘制高质量的元素
            high_quality_elements = [e for e in elements if e['confidence'] >= 0.6 or e.get('is_likely_tag', False)]

            logger.info(f"📊 标注统计: 总元素={len(elements)}, 高质量元素={len(high_quality_elements)}")

            # 绘制每个高质量元素
            for i, element in enumerate(high_quality_elements):
                x, y, w, h = element['rect']
                element_type = element['type']
                confidence = element['confidence']
                center_x, center_y = element['center']
                has_yellow_bg = element.get('has_yellow_background', False)
                is_likely_tag = element.get('is_likely_tag', False)

                # 选择颜色和线条粗细
                if has_yellow_bg:
                    color = yellow_bg_color
                    thickness = 3  # 更粗的线条
                    priority_text = "🏷️"
                elif is_likely_tag:
                    color = high_confidence_color
                    thickness = 2
                    priority_text = "⭐"
                else:
                    color = colors.get(element_type, colors['unknown'])
                    thickness = 1
                    priority_text = ""

                # 绘制轮廓边界
                cv2.rectangle(debug_image, (x, y), (x + w, y + h), color, thickness)

                # 绘制中心点（红色）
                cv2.circle(debug_image, (center_x, center_y), 4, (0, 0, 255), -1)

                # 添加优先级标记
                label = f"{i+1}:{element_type[:4]}{priority_text}"
                cv2.putText(debug_image, label, (x, y - 8),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                # 添加置信度信息
                conf_label = f"{confidence:.2f}"
                cv2.putText(debug_image, conf_label, (x, y + h + 18),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                # 如果有黄色背景，添加特殊标记
                if has_yellow_bg:
                    cv2.putText(debug_image, "YELLOW", (x + w - 50, y + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, yellow_bg_color, 1)

                logger.info(f"  ✅ 标注高质量元素 {i+1}: {element_type} at ({x},{y}) "
                           f"大小({w}x{h}) 置信度{confidence:.2f} "
                           f"黄色背景={has_yellow_bg} 可能标签={is_likely_tag}")

            # 添加区域边界标记
            height, width = left_region.shape[:2]
            cv2.rectangle(debug_image, (0, 0), (width-1, height-1), (255, 255, 255), 2)

            # 添加标题信息
            title = f"High-Quality Tags ({len(high_quality_elements)}/{len(elements)})"
            cv2.putText(debug_image, title, (5, 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 添加图例
            legend_y = 45
            legend_items = [
                ("Green: Tag Button", high_confidence_color),
                ("Cyan: Yellow BG", yellow_bg_color),
                ("Red: Center Point", (0, 0, 255))
            ]

            for i, (text, color) in enumerate(legend_items):
                y_pos = legend_y + i * 20
                cv2.putText(debug_image, text, (5, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # 生成保存路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            debug_filename = f"left_sidebar_optimized_{timestamp}.{self.screenshot_format.lower()}"
            debug_path = os.path.join(self.screenshot_dir, debug_filename)

            # 保存调试截图
            cv2.imwrite(debug_path, debug_image)
            logger.info(f"✅ 优化的左侧区域调试截图已保存: {debug_path}")

            # 输出详细的坐标计算过程（只针对高质量元素）
            logger.info("📊 高质量元素详细坐标计算:")
            window_rect = window_info['rect']
            for i, element in enumerate(high_quality_elements):
                x, y, w, h = element['rect']
                abs_x, abs_y = element['absolute_pos']
                center_x, center_y = element['center']
                abs_center_x, abs_center_y = element['absolute_center']

                logger.info(f"  🎯 元素 {i+1} ({element['type']}):")
                logger.info(f"    相对坐标: ({x}, {y}) 大小: {w}x{h}")
                logger.info(f"    相对中心: ({center_x}, {center_y})")
                logger.info(f"    窗口偏移: ({window_rect[0]}, {window_rect[1]})")
                logger.info(f"    屏幕坐标: ({abs_x}, {abs_y})")
                logger.info(f"    屏幕中心: ({abs_center_x}, {abs_center_y})")
                logger.info(f"    置信度: {element['confidence']:.2f}")
                logger.info(f"    黄色背景: {element.get('has_yellow_background', False)}")
                logger.info(f"    可能标签: {element.get('is_likely_tag', False)}")

        except Exception as e:
            logger.error(f"保存优化的左侧区域调试截图失败: {e}")

    def _save_annotated_screenshot(self, image: np.ndarray, elements: List[Dict], original_path: str) -> None:
        """
        保存带有元素标注的截图，增强了调试功能

        Args:
            image: OpenCV图像数组
            elements: 元素列表
            original_path: 原始截图路径
        """
        try:
            # 创建图像副本用于标注
            annotated_image = image.copy()

            # 为不同类型的元素使用不同颜色
            colors = {
                # 标签专用类型
                'tag_button': (0, 255, 0),      # 绿色 - 标签按钮
                'tag_icon': (255, 0, 0),        # 蓝色 - 标签图标
                'tag_text': (0, 255, 255),      # 黄色 - 标签文本
                # 通用类型
                'button': (0, 200, 0),          # 深绿色 - 普通按钮
                'icon': (200, 0, 0),            # 深蓝色 - 普通图标
                'text_field': (0, 200, 200),    # 深黄色 - 文本框
                'clickable': (255, 0, 255),     # 紫色 - 可点击元素
                'panel': (128, 128, 255),       # 浅紫色 - 面板
                'preset_position': (255, 128, 0), # 橙色 - 预设位置
                'unknown': (128, 128, 128)       # 灰色 - 未知元素
            }

            logger.info(f"🎨 开始标注 {len(elements)} 个元素...")

            # 绘制左侧200像素区域边界
            height, width = annotated_image.shape[:2]
            if width > 200:
                cv2.line(annotated_image, (200, 0), (200, height), (255, 255, 255), 2)
                cv2.putText(annotated_image, "Left 200px", (205, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制元素边框和标签
            for i, element in enumerate(elements):
                x, y, w, h = element['rect']
                element_type = element['type']
                color = colors.get(element_type, colors['unknown'])

                # 获取额外信息
                confidence = element.get('confidence', 0)
                tag_confidence = element.get('tag_confidence', 0)
                detection_region = element.get('detection_region', 'unknown')

                # 绘制轮廓边界（绿色）
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), color, 2)

                # 绘制中心点（红色）
                center_x, center_y = element.get('center', (x + w//2, y + h//2))
                cv2.circle(annotated_image, (center_x, center_y), 3, (0, 0, 255), -1)

                # 添加元素ID和类型标签
                label = f"{i+1}:{element_type[:8]}"
                cv2.putText(annotated_image, label, (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                # 添加置信度信息
                if tag_confidence > 0:
                    conf_label = f"T:{tag_confidence:.2f}"
                    cv2.putText(annotated_image, conf_label, (x, y + h + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
                elif confidence > 0:
                    conf_label = f"C:{confidence:.2f}"
                    cv2.putText(annotated_image, conf_label, (x, y + h + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

                # 添加检测区域标识
                if detection_region == 'left_sidebar':
                    cv2.putText(annotated_image, "L", (x + w - 15, y + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

                # 如果有绝对坐标信息，添加到标注中
                if 'absolute_pos' in element:
                    abs_x, abs_y = element['absolute_pos']
                    abs_label = f"({abs_x},{abs_y})"
                    cv2.putText(annotated_image, abs_label, (x, y + h + 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.25, color, 1)

                logger.info(f"  标注元素 {i+1}: {element_type} at ({x},{y}) "
                           f"大小({w}x{h}) 置信度{confidence:.2f}")

            # 添加图例
            legend_y = 50
            legend_items = [
                ("Green: Tag Button", (0, 255, 0)),
                ("Blue: Tag Icon", (255, 0, 0)),
                ("Yellow: Tag Text", (0, 255, 255)),
                ("Purple: Clickable", (255, 0, 255)),
                ("Red Dot: Center", (0, 0, 255))
            ]

            for i, (text, color) in enumerate(legend_items):
                y_pos = legend_y + i * 20
                cv2.putText(annotated_image, text, (width - 200, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # 添加统计信息
            stats_text = f"Total: {len(elements)} elements"
            cv2.putText(annotated_image, stats_text, (10, height - 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 生成标注截图的保存路径
            base_name = os.path.splitext(original_path)[0]
            annotated_path = f"{base_name}_annotated.{self.screenshot_format.lower()}"

            # 保存标注截图
            cv2.imwrite(annotated_path, annotated_image)
            logger.info(f"✅ 增强标注截图已保存: {annotated_path}")

        except Exception as e:
            logger.error(f"❌ 保存标注截图失败: {e}")

    def find_tag_elements(self, hwnd: int, window_elements: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        查找窗口中与标签相关的元素，优先使用左侧区域检测结果

        Args:
            hwnd: 窗口句柄
            window_elements: 窗口元素信息，如果为None则重新分析

        Returns:
            标签相关元素列表
        """
        try:
            # 获取窗口元素信息
            if window_elements is None:
                window_elements = self.analyze_window_elements(hwnd)

            if not window_elements or 'elements' not in window_elements:
                logger.error("无法获取窗口元素信息")
                return []

            elements = window_elements['elements']
            detection_method = window_elements.get('detection_method', 'unknown')

            logger.info(f"🔍 开始查找标签元素，检测方法: {detection_method}")
            logger.info(f"共有 {len(elements)} 个候选元素")

            # 如果使用了左侧区域检测，直接使用这些元素作为标签候选
            if detection_method == 'left_sidebar_focused':
                logger.info("✅ 使用左侧区域专门检测的结果")
                tag_elements = []

                for element in elements:
                    # 左侧区域检测的元素已经是针对标签优化的
                    tag_element = element.copy()

                    # 根据元素特征设置标签置信度
                    element_type = element.get('type', 'unknown')
                    base_confidence = element.get('confidence', 0.5)
                    has_yellow_bg = element.get('has_yellow_background', False)
                    is_likely_tag = element.get('is_likely_tag', False)
                    detection_method = element.get('detection_method', 'unknown')

                    # 优先级计算：文本检测 > 黄色背景 > 标签按钮 > 其他
                    if detection_method in ['text_position_based', 'known_position']:
                        tag_element['tag_confidence'] = 0.99  # 最高优先级 - 文本检测
                        tag_element['tag_type'] = 'detected_tag_text'
                        tag_element['priority'] = 1
                        logger.info(f"🎯 发现文本检测的标签: {element_type}")
                    elif has_yellow_bg:
                        tag_element['tag_confidence'] = 0.98  # 第二优先级
                        tag_element['tag_type'] = 'yellow_tag_button'
                        tag_element['priority'] = 2
                    elif element_type == 'tag_button':
                        tag_element['tag_confidence'] = min(0.95, base_confidence + 0.15)
                        tag_element['tag_type'] = 'button'
                        tag_element['priority'] = 3
                    elif element_type == 'tag_icon':
                        tag_element['tag_confidence'] = min(0.9, base_confidence + 0.1)
                        tag_element['tag_type'] = 'icon'
                        tag_element['priority'] = 4
                    elif is_likely_tag:
                        tag_element['tag_confidence'] = min(0.85, base_confidence + 0.1)
                        tag_element['tag_type'] = 'likely_tag'
                        tag_element['priority'] = 5
                    elif element_type == 'tag_text':
                        tag_element['tag_confidence'] = base_confidence * 0.8  # 降低文本的优先级
                        tag_element['tag_type'] = 'text_field'
                        tag_element['priority'] = 6
                    else:
                        tag_element['tag_confidence'] = base_confidence * 0.6
                        tag_element['tag_type'] = element_type
                        tag_element['priority'] = 7

                    tag_elements.append(tag_element)

                    x, y, w, h = element['rect']
                    abs_x, abs_y = element['absolute_pos']
                    logger.info(f"  标签候选 {len(tag_elements)}: {element_type} "
                               f"位置({x},{y}) 屏幕({abs_x},{abs_y}) "
                               f"大小({w}x{h}) 置信度={tag_element['tag_confidence']:.2f} "
                               f"优先级={tag_element['priority']} 黄色背景={has_yellow_bg}")

                # 按优先级和置信度排序
                tag_elements.sort(key=lambda x: (x.get('priority', 10), -x.get('tag_confidence', 0)))

                logger.info(f"✅ 左侧区域检测完成，共找到 {len(tag_elements)} 个标签候选元素")

                # 输出最终排序结果
                logger.info("🏆 最终标签候选排序:")
                for i, element in enumerate(tag_elements[:5]):  # 显示前5个
                    x, y = element['rect'][:2]
                    confidence = element.get('tag_confidence', 0)
                    priority = element.get('priority', 10)
                    tag_type = element.get('tag_type', 'unknown')
                    logger.info(f"  排名 {i+1}: {tag_type} 位置({x},{y}) "
                               f"置信度={confidence:.2f} 优先级={priority}")

                return tag_elements

            # 如果使用全窗口检测作为备选，应用传统的标签识别逻辑
            else:
                logger.info("⚠️ 使用全窗口检测备选方法")
                return self._find_tag_elements_fallback(elements, window_elements)

        except Exception as e:
            logger.error(f"查找标签元素失败: {e}")
            return []

    def _find_tag_elements_fallback(self, elements: List[Dict], window_elements: Dict) -> List[Dict[str, Any]]:
        """
        备选的标签元素查找方法（用于全窗口检测）

        Args:
            elements: 检测到的元素列表
            window_elements: 窗口元素信息

        Returns:
            标签相关元素列表
        """
        try:
            tag_elements = []
            logger.info(f"使用备选方法在 {len(elements)} 个元素中查找标签")

            # 策略1: 优先查找左侧区域的元素（x坐标 < 200）
            left_region_elements = [e for e in elements if e['rect'][0] < 200]
            logger.info(f"左侧区域候选元素: {len(left_region_elements)} 个")

            for element in left_region_elements:
                element_type = element['type']
                x, y, w, h = element['rect']
                confidence = element.get('confidence', 0.5)

                # 左侧区域的按钮和可点击元素更可能是标签
                if element_type in ['button', 'clickable']:
                    if (15 <= w <= 180 and 20 <= h <= 60):
                        tag_element = element.copy()
                        tag_element['tag_confidence'] = min(0.9, confidence + 0.3)  # 左侧区域加分
                        tag_element['tag_type'] = 'button'
                        tag_elements.append(tag_element)
                        logger.info(f"发现左侧标签按钮: 位置({x}, {y}), 大小({w}x{h}), 置信度={tag_element['tag_confidence']:.2f}")

                elif element_type == 'icon':
                    if (10 <= w <= 50 and 10 <= h <= 50):
                        tag_element = element.copy()
                        tag_element['tag_confidence'] = min(0.85, confidence + 0.2)
                        tag_element['tag_type'] = 'icon'
                        tag_elements.append(tag_element)
                        logger.info(f"发现左侧标签图标: 位置({x}, {y}), 大小({w}x{h})")

            # 策略2: 如果左侧区域没有找到足够的元素，扩展到全窗口
            if len(tag_elements) < 3:
                logger.info("左侧区域元素不足，扩展到全窗口搜索")
                for element in elements:
                    if element in left_region_elements:
                        continue  # 跳过已处理的左侧元素

                    element_type = element['type']
                    x, y, w, h = element['rect']
                    confidence = element.get('confidence', 0.5)
                    window_width = window_elements['width']
                    window_height = window_elements['height']

                    # 标签按钮识别
                    if element_type in ['button', 'clickable']:
                        in_main_area = (0.05 * window_width <= x <= 0.95 * window_width and
                                       0.05 * window_height <= y <= 0.8 * window_height)
                        size_appropriate = (20 <= w <= 300 and 15 <= h <= 80)

                        if in_main_area and size_appropriate:
                            tag_element = element.copy()
                            tag_element['tag_confidence'] = confidence
                            tag_element['tag_type'] = 'button'
                            tag_elements.append(tag_element)

            # 策略3: 如果仍然没有找到，使用预设位置（重点关注左侧）
            if not tag_elements:
                logger.warning("未找到标签元素，使用预设的左侧位置")
                window_width = window_elements['width']
                window_height = window_elements['height']

                # 重点关注左侧200像素区域的预设位置
                left_positions = [
                    (50, int(window_height * 0.2), 100, 35),   # 左侧上方
                    (30, int(window_height * 0.3), 120, 40),   # 左侧中上
                    (40, int(window_height * 0.4), 110, 38),   # 左侧中央
                    (60, int(window_height * 0.5), 90, 32),    # 左侧中下
                ]

                for i, (px, py, pw, ph) in enumerate(left_positions):
                    if px + pw <= 200:  # 确保在左侧200像素内
                        tag_element = {
                            'id': f'left_preset_{i}',
                            'type': 'preset_position',
                            'rect': (px, py, pw, ph),
                            'center': (px + pw//2, py + ph//2),
                            'area': pw * ph,
                            'aspect_ratio': pw / ph,
                            'confidence': 0.4,
                            'absolute_pos': (window_elements['rect'][0] + px, window_elements['rect'][1] + py),
                            'absolute_center': (window_elements['rect'][0] + px + pw//2, window_elements['rect'][1] + py + ph//2),
                            'tag_confidence': 0.4,
                            'tag_type': 'preset_left_position',
                            'detection_region': 'preset_left'
                        }
                        tag_elements.append(tag_element)
                        logger.info(f"添加左侧预设位置 {i+1}: 位置({px}, {py}), 大小({pw}x{ph})")

            # 按置信度排序
            tag_elements.sort(key=lambda x: x.get('tag_confidence', 0), reverse=True)

            logger.info(f"备选方法找到 {len(tag_elements)} 个可能的标签相关元素")
            return tag_elements

        except Exception as e:
            logger.error(f"备选标签查找方法失败: {e}")
            return []

    def execute_tag_operation(self, hwnd: int, operation: str = "VIEW",
                            tag_text: str = "", position: Optional[Tuple[int, int]] = None,
                            skip_activation: bool = False) -> bool:
        """
        执行标签操作的主入口函数

        Args:
            hwnd: 窗口句柄
            operation: 操作类型 ("VIEW", "CLICK", "EDIT", "ADD", "DELETE", "SEARCH", "CLICK_TAG_THEN_UNSWIPED")
            tag_text: 标签文本内容（用于编辑、添加、搜索操作）
            position: 指定位置（可选，用于精确点击）
            skip_activation: 是否跳过窗口激活（防止循环）

        Returns:
            操作是否成功
        """
        try:
            logger.info(f"开始执行标签操作: {operation}")

            # 验证窗口
            if not self.verify_wechat_window(hwnd):
                logger.error("窗口验证失败")
                return False

            # 只在需要时激活窗口，防止循环
            if not skip_activation:
                if not self.activate_window(hwnd):
                    logger.error("窗口激活失败")
                    return False
                # 等待窗口稳定
                time.sleep(self.medium_delay)
            else:
                logger.info("跳过窗口激活，使用当前窗口状态")

            # 获取窗口截图和元素信息（跳过重复激活）
            screenshot_path = self.capture_window_screenshot(hwnd, skip_activation=True)
            if not screenshot_path:
                logger.error("无法获取窗口截图")
                return False

            # 分析窗口元素
            window_elements = self.analyze_window_elements(hwnd, screenshot_path)
            if not window_elements:
                logger.error("无法分析窗口元素")
                return False

            # 查找标签相关元素
            tag_elements = self.find_tag_elements(hwnd, window_elements)
            if not tag_elements and operation != "ADD":
                logger.warning("未找到标签相关元素")
                if operation == "VIEW":
                    logger.info("VIEW操作：当前窗口中未检测到标签元素")
                    return True  # VIEW操作即使没找到元素也算成功
                return False

            # 根据操作类型执行相应操作
            success = False
            if operation == "VIEW":
                success = self._view_tags(tag_elements, window_elements)
            elif operation == "CLICK":
                success = self._click_tag_element(hwnd, tag_elements, position)
            elif operation == "CLICK_TAG_THEN_UNSWIPED":
                # 复合操作：先点击标签，再点击未刷
                success = self._execute_compound_tag_unswiped_operation(hwnd, tag_elements, position)
            elif operation == "EDIT":
                success = self._edit_tag_content(hwnd, tag_elements, tag_text)
            elif operation == "ADD":
                success = self._add_new_tag(hwnd, window_elements, tag_text)
            elif operation == "DELETE":
                success = self._delete_tag(hwnd, tag_elements, tag_text)
            elif operation == "SEARCH":
                success = self._search_tag(hwnd, window_elements, tag_text)
            else:
                logger.error(f"不支持的操作类型: {operation}")
                return False

            # 验证操作结果
            if success and getattr(globals(), 'VERIFY_OPERATION_SUCCESS', True):
                time.sleep(getattr(globals(), 'VERIFICATION_TIMEOUT', 2))
                success = self.verify_tag_operation_result(hwnd, operation, tag_text)

            if success:
                logger.info(f"标签操作 {operation} 执行成功")
            else:
                logger.error(f"标签操作 {operation} 执行失败")

            return success

        except Exception as e:
            logger.error(f"执行标签操作失败: {e}")
            return False

    def _view_tags(self, tag_elements: List[Dict], window_elements: Dict) -> bool:
        """
        查看标签信息

        Args:
            tag_elements: 标签元素列表
            window_elements: 窗口元素信息

        Returns:
            操作是否成功
        """
        try:
            logger.info("=== 标签元素查看结果 ===")
            logger.info(f"窗口大小: {window_elements['width']}x{window_elements['height']}")
            logger.info(f"发现 {len(tag_elements)} 个可能的标签元素:")

            for i, element in enumerate(tag_elements):
                x, y, w, h = element['rect']
                tag_type = element.get('tag_type', 'unknown')
                confidence = element.get('tag_confidence', 0)
                abs_x, abs_y = element['absolute_pos']

                logger.info(f"  标签元素 {i+1}:")
                logger.info(f"    类型: {tag_type}")
                logger.info(f"    位置: ({x}, {y}) -> 屏幕坐标({abs_x}, {abs_y})")
                logger.info(f"    大小: {w}x{h}")
                logger.info(f"    置信度: {confidence:.2f}")
                logger.info(f"    中心点: {element['center']}")

            if tag_elements:
                best_element = tag_elements[0]
                logger.info(f"推荐操作目标: 标签元素1 (置信度: {best_element.get('tag_confidence', 0):.2f})")

            return True

        except Exception as e:
            logger.error(f"查看标签失败: {e}")
            return False

    def _click_tag_element(self, hwnd: int, tag_elements: List[Dict],
                          position: Optional[Tuple[int, int]] = None) -> bool:
        """
        点击标签元素，优化了左侧区域的点击位置计算

        Args:
            hwnd: 窗口句柄
            tag_elements: 标签元素列表
            position: 指定点击位置

        Returns:
            操作是否成功
        """
        try:
            if position:
                # 使用指定位置
                click_x, click_y = position
                logger.info(f"🎯 使用指定位置点击: ({click_x}, {click_y})")

                # 验证指定位置是否在左侧区域
                if click_x <= 200:
                    logger.info("✅ 指定位置在左侧标签区域内")
                else:
                    logger.warning(f"⚠️ 指定位置 x={click_x} 超出左侧区域范围")

            else:
                # 选择最佳标签元素
                if not tag_elements:
                    logger.error("❌ 没有可点击的标签元素")
                    return False

                best_element = tag_elements[0]  # 已按置信度排序

                # 获取元素信息
                element_type = best_element.get('type', 'unknown')
                tag_type = best_element.get('tag_type', 'unknown')
                confidence = best_element.get('tag_confidence', 0)
                detection_region = best_element.get('detection_region', 'unknown')

                # 优先使用预计算的绝对中心坐标
                if 'absolute_center' in best_element:
                    click_x, click_y = best_element['absolute_center']
                    logger.info(f"🎯 使用预计算的绝对中心坐标: ({click_x}, {click_y})")
                else:
                    # 备选方案：手动计算
                    abs_x, abs_y = best_element['absolute_pos']
                    w, h = best_element['rect'][2], best_element['rect'][3]
                    click_x = abs_x + w // 2
                    click_y = abs_y + h // 2
                    logger.info(f"🎯 手动计算点击坐标: ({click_x}, {click_y})")

                logger.info(f"📊 选择的标签元素详情:")
                logger.info(f"   类型: {element_type} -> 标签类型: {tag_type}")
                logger.info(f"   检测区域: {detection_region}")
                logger.info(f"   置信度: {confidence:.2f}")
                logger.info(f"   相对位置: {best_element['rect']}")
                logger.info(f"   绝对位置: {best_element['absolute_pos']}")
                logger.info(f"   最终点击坐标: ({click_x}, {click_y})")

                # 验证点击位置
                if click_x <= 200:
                    logger.info("✅ 点击位置在左侧标签区域内")
                else:
                    logger.warning(f"⚠️ 点击位置 x={click_x} 超出左侧区域，可能存在偏移问题")

                    # 如果点击位置超出左侧区域，尝试修正
                    if detection_region == 'left_sidebar':
                        # 对于左侧区域检测的元素，强制限制在左侧200像素内
                        original_x = click_x
                        click_x = min(click_x, 200)
                        logger.warning(f"🔧 修正点击位置: {original_x} -> {click_x}")

            # 确保窗口激活
            if not self.activate_window(hwnd):
                logger.warning("⚠️ 窗口激活失败，但继续尝试点击")

            # 移动鼠标到目标位置
            logger.info(f"🖱️ 移动鼠标到目标位置: ({click_x}, {click_y})")
            pyautogui.moveTo(click_x, click_y, duration=getattr(globals(), 'MOUSE_MOVE_DURATION', 0.5))
            time.sleep(self.short_delay)

            # 执行点击
            logger.info(f"🖱️ 执行点击操作: ({click_x}, {click_y})")
            pyautogui.click(click_x, click_y, duration=getattr(globals(), 'CLICK_DURATION', 0.1))

            # 记录点击结果
            logger.info(f"✅ 点击操作完成")
            logger.info(f"   最终点击坐标: ({click_x}, {click_y})")
            logger.info(f"   是否在左侧区域: {'是' if click_x <= 200 else '否'}")

            # 等待响应
            time.sleep(self.medium_delay)

            return True

        except Exception as e:
            logger.error(f"❌ 点击标签元素失败: {e}")
            return False

    def _edit_tag_content(self, hwnd: int, tag_elements: List[Dict], tag_text: str) -> bool:
        """
        编辑标签内容

        Args:
            hwnd: 窗口句柄
            tag_elements: 标签元素列表
            tag_text: 新的标签文本

        Returns:
            操作是否成功
        """
        try:
            if not tag_text:
                logger.error("编辑标签需要提供标签文本")
                return False

            # 查找文本输入框类型的元素
            text_elements = [e for e in tag_elements if e.get('tag_type') == 'text_field']
            if not text_elements:
                # 如果没有文本框，尝试点击按钮来激活编辑模式
                button_elements = [e for e in tag_elements if e.get('tag_type') == 'button']
                if button_elements:
                    logger.info("先点击标签按钮激活编辑模式")
                    if not self._click_tag_element(hwnd, button_elements):
                        return False
                    time.sleep(self.medium_delay)

                    # 重新分析窗口寻找文本输入框
                    window_elements = self.analyze_window_elements(hwnd)
                    tag_elements = self.find_tag_elements(hwnd, window_elements)
                    text_elements = [e for e in tag_elements if e.get('tag_type') == 'text_field']

            if not text_elements:
                logger.error("未找到可编辑的文本区域")
                return False

            # 选择最佳文本元素
            best_text_element = text_elements[0]
            abs_x, abs_y = best_text_element['absolute_pos']
            w, h = best_text_element['rect'][2], best_text_element['rect'][3]

            # 点击文本框
            click_x = abs_x + w // 2
            click_y = abs_y + h // 2

            logger.info(f"点击文本框进入编辑模式: ({click_x}, {click_y})")
            pyautogui.click(click_x, click_y)
            time.sleep(self.short_delay)

            # 选择所有文本（Ctrl+A）
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)

            # 输入新文本
            logger.info(f"输入标签文本: {tag_text}")
            pyautogui.write(tag_text, interval=getattr(globals(), 'TAG_TEXT_INPUT_DELAY', 0.1))

            # 按回车确认
            pyautogui.press('enter')
            time.sleep(self.medium_delay)

            logger.info(f"标签文本编辑完成: {tag_text}")
            return True

        except Exception as e:
            logger.error(f"编辑标签内容失败: {e}")
            return False

    def _execute_compound_tag_unswiped_operation(self, hwnd: int, tag_elements: List[Dict],
                                               position: Optional[Tuple[int, int]] = None) -> bool:
        """
        执行复合操作：点击标签然后点击未刷

        Args:
            hwnd: 窗口句柄
            tag_elements: 标签元素列表
            position: 指定位置（可选）

        Returns:
            操作是否成功
        """
        try:
            logger.info("🎯 开始执行复合操作：标签 → 未刷")

            # 第一步：点击"标签"菜单项
            logger.info("📋 步骤1: 点击'标签'菜单项")
            tag_click_success = self._click_tag_element(hwnd, tag_elements, position)

            if not tag_click_success:
                logger.error("❌ 第一步失败：无法点击标签菜单项")
                return False

            logger.info("✅ 第一步成功：标签菜单项点击完成")

            # 等待界面加载
            logger.info(f"⏳ 等待界面加载 {self.tag_to_unswiped_delay} 秒...")
            time.sleep(self.tag_to_unswiped_delay)

            # 第二步：检测并点击"未刷"选项
            logger.info("📋 步骤2: 检测并点击'未刷'选项")

            # 导入复合操作模块
            try:
                import compound_operations

                # 重新截图获取最新界面
                screenshot_path = self.capture_window_screenshot(hwnd, skip_activation=True)
                if not screenshot_path:
                    logger.error("❌ 无法获取更新后的窗口截图")
                    return False

                # 分析窗口元素
                window_elements = self.analyze_window_elements(hwnd, screenshot_path)
                if not window_elements:
                    logger.error("❌ 无法分析更新后的窗口元素")
                    return False

                # 查找"未刷"选项
                unswiped_elements = compound_operations.find_unswiped_elements(self, hwnd, window_elements)
                if not unswiped_elements:
                    logger.warning("⚠️ 未找到'未刷'选项，可能界面未正确加载")
                    return False

                # 点击"未刷"选项
                unswiped_click_success = compound_operations.click_unswiped_element(self, hwnd, unswiped_elements)

                if unswiped_click_success:
                    logger.info("✅ 第二步成功：未刷选项点击完成")
                    logger.info("🎉 复合操作完全成功：标签 → 未刷")
                    return True
                else:
                    logger.error("❌ 第二步失败：无法点击未刷选项")
                    logger.info("⚠️ 复合操作部分成功：标签点击成功，但未刷点击失败")
                    return False

            except ImportError:
                logger.error("❌ 无法导入复合操作模块，请确保compound_operations.py文件存在")
                return False

        except Exception as e:
            logger.error(f"复合操作失败: {e}")
            return False

    def _add_new_tag(self, hwnd: int, window_elements: Dict, tag_text: str) -> bool:
        """
        添加新标签

        Args:
            hwnd: 窗口句柄
            window_elements: 窗口元素信息
            tag_text: 标签文本

        Returns:
            操作是否成功
        """
        try:
            if not tag_text:
                logger.error("添加标签需要提供标签文本")
                return False

            logger.info(f"尝试添加新标签: {tag_text}")

            # 查找可能的"添加"或"+"按钮
            elements = window_elements.get('elements', [])
            add_buttons = []

            for element in elements:
                x, y, w, h = element['rect']
                element_type = element['type']

                # 查找小的正方形按钮（可能是+按钮）
                if (element_type == 'button' and
                    15 <= w <= 50 and 15 <= h <= 50 and
                    0.7 <= element['aspect_ratio'] <= 1.3):
                    add_buttons.append(element)

                # 查找可能的"添加"按钮
                elif (element_type == 'button' and
                      50 <= w <= 150 and 20 <= h <= 40):
                    add_buttons.append(element)

            if not add_buttons:
                logger.warning("未找到添加按钮，尝试使用右键菜单")
                # 尝试在窗口中央右键点击
                window_width = window_elements['width']
                window_height = window_elements['height']
                window_rect = window_elements['rect']

                right_click_x = window_rect[0] + window_width // 2
                right_click_y = window_rect[1] + window_height // 2

                pyautogui.rightClick(right_click_x, right_click_y)
                time.sleep(self.medium_delay)

                # 尝试输入文本（可能会打开输入框）
                pyautogui.write(tag_text, interval=0.1)
                pyautogui.press('enter')
                time.sleep(self.medium_delay)

                return True

            # 点击添加按钮
            best_add_button = add_buttons[0]
            abs_x, abs_y = best_add_button['absolute_pos']
            w, h = best_add_button['rect'][2], best_add_button['rect'][3]

            click_x = abs_x + w // 2
            click_y = abs_y + h // 2

            logger.info(f"点击添加按钮: ({click_x}, {click_y})")
            pyautogui.click(click_x, click_y)
            time.sleep(self.medium_delay)

            # 输入标签文本
            logger.info(f"输入新标签文本: {tag_text}")
            pyautogui.write(tag_text, interval=0.1)
            pyautogui.press('enter')
            time.sleep(self.medium_delay)

            logger.info(f"新标签添加完成: {tag_text}")
            return True

        except Exception as e:
            logger.error(f"添加新标签失败: {e}")
            return False

    def _delete_tag(self, hwnd: int, tag_elements: List[Dict], tag_text: str = "") -> bool:
        """
        删除标签

        Args:
            hwnd: 窗口句柄
            tag_elements: 标签元素列表
            tag_text: 要删除的标签文本（可选）

        Returns:
            操作是否成功
        """
        try:
            logger.info(f"尝试删除标签: {tag_text if tag_text else '(选择的标签)'}")

            if not tag_elements:
                logger.error("没有可删除的标签元素")
                return False

            # 选择要删除的标签元素
            target_element = tag_elements[0]  # 默认选择第一个

            # 右键点击标签元素打开上下文菜单
            abs_x, abs_y = target_element['absolute_pos']
            w, h = target_element['rect'][2], target_element['rect'][3]

            click_x = abs_x + w // 2
            click_y = abs_y + h // 2

            logger.info(f"右键点击标签元素: ({click_x}, {click_y})")
            pyautogui.rightClick(click_x, click_y)
            time.sleep(self.medium_delay)

            # 尝试按Delete键
            logger.info("尝试按Delete键删除标签")
            pyautogui.press('delete')
            time.sleep(self.short_delay)

            # 如果有确认对话框，按回车确认
            pyautogui.press('enter')
            time.sleep(self.medium_delay)

            logger.info("标签删除操作完成")
            return True

        except Exception as e:
            logger.error(f"删除标签失败: {e}")
            return False

    def _search_tag(self, hwnd: int, window_elements: Dict, tag_text: str) -> bool:
        """
        搜索标签

        Args:
            hwnd: 窗口句柄
            window_elements: 窗口元素信息
            tag_text: 搜索的标签文本

        Returns:
            操作是否成功
        """
        try:
            if not tag_text:
                logger.error("搜索标签需要提供搜索文本")
                return False

            logger.info(f"搜索标签: {tag_text}")

            # 尝试使用Ctrl+F打开搜索框
            pyautogui.hotkey('ctrl', 'f')
            time.sleep(self.medium_delay)

            # 输入搜索文本
            pyautogui.write(tag_text, interval=0.1)
            pyautogui.press('enter')
            time.sleep(self.medium_delay)

            logger.info(f"标签搜索完成: {tag_text}")
            return True

        except Exception as e:
            logger.error(f"搜索标签失败: {e}")
            return False

    def verify_tag_operation_result(self, hwnd: int, operation: str, tag_text: str = "") -> bool:
        """
        验证标签操作结果

        Args:
            hwnd: 窗口句柄
            operation: 执行的操作类型
            tag_text: 相关的标签文本

        Returns:
            验证是否成功
        """
        try:
            logger.info(f"验证标签操作结果: {operation}")

            # 等待界面更新
            time.sleep(getattr(globals(), 'TAG_OPERATION_VERIFY_DELAY', 2))

            # 重新截图验证
            if getattr(globals(), 'VERIFICATION_SCREENSHOT', True):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                verify_screenshot_path = os.path.join(
                    self.screenshot_dir,
                    f"verify_{operation}_{timestamp}.{self.screenshot_format.lower()}"
                )

                result_screenshot = self.capture_window_screenshot(hwnd, verify_screenshot_path, skip_activation=True)
                if result_screenshot:
                    logger.info(f"验证截图已保存: {result_screenshot}")

            # 简单验证：检查窗口是否仍然有效
            if not self.verify_wechat_window(hwnd):
                logger.warning("操作后窗口验证失败")
                return False

            # 根据操作类型进行特定验证
            if operation in ["CLICK", "VIEW"]:
                # 对于点击和查看操作，只要窗口有效就认为成功
                logger.info("点击/查看操作验证通过")
                return True
            elif operation in ["EDIT", "ADD", "DELETE"]:
                # 对于修改操作，可以进行更详细的验证
                # 这里简化处理，实际应用中可以添加更复杂的验证逻辑
                logger.info(f"{operation}操作验证通过")
                return True
            elif operation == "SEARCH":
                # 搜索操作的验证
                logger.info("搜索操作验证通过")
                return True

            return True

        except Exception as e:
            logger.error(f"验证标签操作结果失败: {e}")
            return False

    def execute_complete_tag_workflow(self, operation: str = "VIEW", tag_text: str = "") -> bool:
        """
        执行完整的标签操作工作流程

        Args:
            operation: 操作类型 ("VIEW", "CLICK", "EDIT", "ADD", "DELETE", "SEARCH", "CLICK_TAG_THEN_UNSWIPED")
            tag_text: 标签文本内容

        Returns:
            操作是否成功
        """
        try:
            logger.info("=" * 60)
            logger.info("开始执行完整的标签操作工作流程")
            logger.info("=" * 60)

            # 步骤1: 检查微信是否运行
            if not self.is_wechat_running():
                logger.error("微信未运行，请先启动微信应用程序")
                return False

            # 步骤2: 查找并激活微信窗口
            hwnd = self.find_wechat_window()
            if not hwnd:
                logger.error("无法找到微信窗口")
                return False

            if not self.activate_window(hwnd):
                logger.error("无法激活微信窗口")
                return False

            # 步骤3: 确保在通讯录界面
            if not self.check_if_already_in_contacts(hwnd):
                logger.info("当前不在通讯录界面，正在导航...")

                # 获取窗口位置信息
                window_rect = self.get_window_rect(hwnd)
                if not window_rect:
                    logger.error("无法获取微信窗口位置信息")
                    return False

                # 点击通讯录按钮
                if not self.click_contacts_button(window_rect):
                    logger.error("无法点击通讯录按钮")
                    return False

                # 点击通讯录管理
                if not self.click_manage_contacts(window_rect):
                    logger.error("无法访问通讯录管理")
                    return False

            # 步骤4: 执行标签操作
            logger.info(f"开始执行标签操作: {operation}")
            # 由于窗口已经激活，跳过重复激活避免循环
            success = self.execute_tag_operation(hwnd, operation, tag_text, skip_activation=True)

            if success:
                logger.info("✅ 标签操作工作流程执行成功！")
            else:
                logger.error("❌ 标签操作工作流程执行失败！")

            return success

        except Exception as e:
            logger.error(f"执行标签操作工作流程失败: {e}")
            return False

def main():
    """主函数 - 完全自动化模式"""
    print("=" * 60)
    print("微信通讯录标签自动化脚本 - 完全自动化模式")
    print("无需用户交互，自动执行完整的标签操作流程")
    print("=" * 60)
    print()

    try:
        # 创建自动化实例
        automation = WeChatContactsAutomation()

        print("🚀 开始执行完全自动化流程...")
        print("执行顺序：基础激活 → 查看标签元素 → 自动点击标签")
        print()

        # 步骤1: 执行基础自动化流程（激活通讯录管理窗口）
        print("📋 步骤1: 执行基础自动化流程（激活微信通讯录窗口）")
        print("-" * 50)
        success_step1 = automation.execute_automation()
        if success_step1:
            print("✅ 步骤1完成：基础自动化流程执行成功！")
        else:
            print("❌ 步骤1失败：基础自动化流程执行失败！")
            print("程序终止，请检查微信是否正常运行。")
            return

        print()

        # 步骤2: 自动查看标签元素
        print("🔍 步骤2: 自动查看和分析标签元素")
        print("-" * 50)
        success_step2 = automation.execute_complete_tag_workflow("VIEW")
        if success_step2:
            print("✅ 步骤2完成：标签元素查看和分析成功！")
            print("   详细的元素信息已记录在日志中。")
        else:
            print("❌ 步骤2失败：标签元素查看失败！")
            print("   继续尝试执行点击操作...")

        print()

        # 步骤3: 自动点击标签
        print("🖱️  步骤3: 自动执行标签点击操作")
        print("-" * 50)
        success_step3 = automation.execute_complete_tag_workflow("CLICK")
        if success_step3:
            print("✅ 步骤3完成：标签点击操作成功！")
        else:
            print("❌ 步骤3失败：标签点击操作失败！")

        print()
        print("=" * 60)

        # 总结执行结果
        total_steps = 3
        successful_steps = sum([success_step1, success_step2, success_step3])

        print(f"📊 自动化流程执行完成！")
        print(f"   成功步骤: {successful_steps}/{total_steps}")
        print(f"   成功率: {(successful_steps/total_steps)*100:.1f}%")

        if successful_steps == total_steps:
            print("🎉 所有步骤执行成功！微信通讯录标签操作已完成。")
        elif successful_steps >= 2:
            print("⚠️  部分步骤执行成功，主要功能已完成。")
        else:
            print("❌ 多个步骤执行失败，请检查微信状态和网络连接。")

        print()
        print("📝 详细执行日志已保存到 wechat_automation.log 文件中。")
        print("📸 相关截图已保存到 screenshots 目录中。")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        logger.info("用户手动中断自动化流程")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        logger.error(f"未预期的错误: {e}", exc_info=True)

    print("\n🏁 程序结束。")


if __name__ == "__main__":
    main()
