#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的截图功能模块
解决截图问题，提供多种截图方法和自动清理功能
"""

import os
import time
import logging
import cv2
import numpy as np
import pyautogui
import win32gui
import win32ui
import win32con
from PIL import Image, ImageGrab
from datetime import datetime
from typing import Optional, Tuple, List
import glob

# 导入配置
try:
    from config import *
except ImportError:
    # 默认配置
    SCREENSHOT_DIR = "screenshots"
    SCREENSHOT_FORMAT = "PNG"
    MAX_SCREENSHOTS_TO_KEEP = 5
    AUTO_CLEANUP_SCREENSHOTS = True
    MAX_SCREENSHOT_RETRIES = 3
    SCREENSHOT_RETRY_DELAY = 1

logger = logging.getLogger(__name__)

class EnhancedScreenshot:
    """增强的截图功能类"""
    
    def __init__(self):
        """初始化截图功能"""
        self.screenshot_dir = SCREENSHOT_DIR
        self.screenshot_format = SCREENSHOT_FORMAT
        self.max_retries = getattr(globals(), 'MAX_SCREENSHOT_RETRIES', 3)
        self.retry_delay = getattr(globals(), 'SCREENSHOT_RETRY_DELAY', 1)
        
        # 创建截图目录
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
            logger.info(f"创建截图目录: {self.screenshot_dir}")
    
    def cleanup_old_screenshots(self, keep_count: int = None) -> bool:
        """
        清理旧的截图文件
        
        Args:
            keep_count: 保留的文件数量，默认使用配置值
            
        Returns:
            清理是否成功
        """
        try:
            if keep_count is None:
                keep_count = getattr(globals(), 'MAX_SCREENSHOTS_TO_KEEP', 5)
            
            logger.info(f"🧹 开始清理旧截图，保留最新 {keep_count} 个文件")
            
            # 获取所有截图文件
            pattern = os.path.join(self.screenshot_dir, "*.png")
            screenshot_files = glob.glob(pattern)
            
            # 添加其他格式
            for ext in ['*.jpg', '*.jpeg']:
                pattern = os.path.join(self.screenshot_dir, ext)
                screenshot_files.extend(glob.glob(pattern))
            
            if len(screenshot_files) <= keep_count:
                logger.info(f"截图文件数量({len(screenshot_files)})未超过限制({keep_count})，无需清理")
                return True
            
            # 按修改时间排序，最新的在前
            screenshot_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # 删除多余的文件
            files_to_delete = screenshot_files[keep_count:]
            deleted_count = 0
            
            for file_path in files_to_delete:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    logger.debug(f"已删除旧截图: {os.path.basename(file_path)}")
                except Exception as e:
                    logger.warning(f"删除截图文件失败 {file_path}: {e}")
            
            logger.info(f"✅ 截图清理完成，成功删除 {deleted_count} 个旧文件")
            return True
            
        except Exception as e:
            logger.error(f"清理截图文件失败: {e}")
            return False
    
    def capture_window_screenshot_enhanced(self, hwnd: int, save_path: Optional[str] = None,
                                         method: str = "auto") -> Optional[str]:
        """
        增强的窗口截图功能，支持多种截图方法
        
        Args:
            hwnd: 窗口句柄
            save_path: 保存路径
            method: 截图方法 ("auto", "pyautogui", "pil", "win32")
            
        Returns:
            截图文件路径，失败返回None
        """
        try:
            # 验证窗口
            if not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd):
                logger.error("窗口不存在或不可见")
                return None
            
            # 获取窗口位置
            window_rect = win32gui.GetWindowRect(hwnd)
            left, top, right, bottom = window_rect
            width = right - left
            height = bottom - top
            
            logger.info(f"准备截图窗口: 位置({left}, {top}), 大小({width}x{height})")
            
            # 激活窗口
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
            except Exception as e:
                logger.warning(f"窗口激活失败，继续尝试截图: {e}")
            
            # 生成保存路径
            if save_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"wechat_window_{timestamp}.{self.screenshot_format.lower()}"
                save_path = os.path.join(self.screenshot_dir, filename)
            
            # 尝试多种截图方法
            screenshot = None
            methods_to_try = []
            
            if method == "auto":
                methods_to_try = ["pyautogui", "pil", "win32"]
            else:
                methods_to_try = [method]
            
            for retry in range(self.max_retries):
                for method_name in methods_to_try:
                    try:
                        logger.info(f"尝试使用 {method_name} 方法截图 (第{retry+1}次)")
                        
                        if method_name == "pyautogui":
                            screenshot = self._screenshot_pyautogui(left, top, width, height)
                        elif method_name == "pil":
                            screenshot = self._screenshot_pil(left, top, right, bottom)
                        elif method_name == "win32":
                            screenshot = self._screenshot_win32(hwnd, width, height)
                        
                        if screenshot is not None:
                            # 验证截图质量
                            if self._validate_screenshot(screenshot):
                                screenshot.save(save_path, format=self.screenshot_format)
                                logger.info(f"✅ 截图成功保存: {save_path} (方法: {method_name})")
                                return save_path
                            else:
                                logger.warning(f"截图质量验证失败，尝试其他方法")
                                screenshot = None
                        
                    except Exception as e:
                        logger.warning(f"{method_name} 截图方法失败: {e}")
                        continue
                
                if screenshot is None and retry < self.max_retries - 1:
                    logger.warning(f"第{retry+1}次截图失败，{self.retry_delay}秒后重试...")
                    time.sleep(self.retry_delay)
            
            logger.error("所有截图方法都失败了")
            return None
            
        except Exception as e:
            logger.error(f"增强截图功能失败: {e}")
            return None
    
    def _screenshot_pyautogui(self, left: int, top: int, width: int, height: int) -> Optional[Image.Image]:
        """使用pyautogui截图"""
        try:
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
            return screenshot
        except Exception as e:
            logger.debug(f"pyautogui截图失败: {e}")
            return None
    
    def _screenshot_pil(self, left: int, top: int, right: int, bottom: int) -> Optional[Image.Image]:
        """使用PIL截图"""
        try:
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
            return screenshot
        except Exception as e:
            logger.debug(f"PIL截图失败: {e}")
            return None
    
    def _screenshot_win32(self, hwnd: int, width: int, height: int) -> Optional[Image.Image]:
        """使用Win32 API截图"""
        try:
            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制窗口内容
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            if result:
                # 获取位图数据
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                
                # 转换为PIL图像
                screenshot = Image.frombuffer(
                    'RGB',
                    (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                    bmpstr, 'raw', 'BGRX', 0, 1
                )
                
                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                
                return screenshot
            else:
                logger.debug("Win32 BitBlt操作失败")
                return None
                
        except Exception as e:
            logger.debug(f"Win32截图失败: {e}")
            return None
    
    def _validate_screenshot(self, screenshot: Image.Image) -> bool:
        """
        验证截图质量
        
        Args:
            screenshot: PIL图像对象
            
        Returns:
            截图是否有效
        """
        try:
            if screenshot is None:
                return False
            
            # 检查图像尺寸
            width, height = screenshot.size
            if width < 100 or height < 100:
                logger.warning(f"截图尺寸过小: {width}x{height}")
                return False
            
            # 转换为numpy数组进行分析
            img_array = np.array(screenshot)
            
            # 检查是否为纯色图像（可能是截图失败）
            if len(img_array.shape) == 3:
                # 计算颜色方差
                color_variance = np.var(img_array)
                if color_variance < 100:  # 方差太小说明颜色过于单一
                    logger.warning(f"截图颜色方差过小: {color_variance}")
                    return False
            
            logger.debug(f"截图质量验证通过: {width}x{height}, 方差: {color_variance:.2f}")
            return True
            
        except Exception as e:
            logger.warning(f"截图质量验证失败: {e}")
            return False
    
    def capture_and_cleanup(self, hwnd: int, cleanup_first: bool = True) -> Optional[str]:
        """
        截图并清理旧文件的组合操作
        
        Args:
            hwnd: 窗口句柄
            cleanup_first: 是否先清理旧截图
            
        Returns:
            新截图的路径
        """
        try:
            # 先清理旧截图
            if cleanup_first:
                self.cleanup_old_screenshots()
            
            # 进行新截图
            screenshot_path = self.capture_window_screenshot_enhanced(hwnd)
            
            if screenshot_path:
                logger.info(f"✅ 截图和清理操作完成: {screenshot_path}")
                return screenshot_path
            else:
                logger.error("❌ 截图操作失败")
                return None
                
        except Exception as e:
            logger.error(f"截图和清理操作失败: {e}")
            return None

# 创建全局实例
enhanced_screenshot = EnhancedScreenshot()

def get_enhanced_screenshot_instance():
    """获取增强截图实例"""
    return enhanced_screenshot

if __name__ == "__main__":
    # 测试增强截图功能
    print("🧪 测试增强截图功能")
    
    # 查找微信窗口进行测试
    def find_wechat_window():
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if "微信" in window_title or "WeChat" in window_title:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows[0][0] if windows else None
    
    hwnd = find_wechat_window()
    if hwnd:
        screenshot_instance = get_enhanced_screenshot_instance()
        result = screenshot_instance.capture_and_cleanup(hwnd)
        print(f"测试结果: {'成功' if result else '失败'}")
        if result:
            print(f"截图保存至: {result}")
    else:
        print("未找到微信窗口")
