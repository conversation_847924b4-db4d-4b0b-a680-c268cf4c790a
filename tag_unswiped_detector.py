#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门针对标签界面的"未刷"选项检测器
优化检测算法，专门处理标签界面的"未刷"按钮
"""

import cv2
import numpy as np
import os
import logging
from typing import List, Dict, Optional, Tuple
from PIL import Image
import pyautogui
import pytesseract

# 导入现有模块
from wechat_contacts_automation import WeChatContactsAutomation
from enhanced_screenshot import get_enhanced_screenshot_instance

logger = logging.getLogger(__name__)

class TagUnswipedDetector:
    """专门针对标签界面的"未刷"选项检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.automation = WeChatContactsAutomation()
        self.screenshot_handler = get_enhanced_screenshot_instance()
        
        # 标签界面"未刷"选项的特征
        self.unswiped_keywords = ["未刷", "未读", "unread", "unswiped"]
        self.unswiped_colors = [
            # 常见的"未刷"按钮颜色（HSV范围）
            (np.array([100, 50, 50]), np.array([130, 255, 255])),  # 蓝色
            (np.array([0, 0, 150]), np.array([180, 30, 255])),     # 浅灰色
            (np.array([0, 100, 100]), np.array([10, 255, 255])),   # 红色
        ]
        
        logger.info("🎯 标签界面'未刷'检测器初始化完成")
    
    def detect_and_click_unswiped(self, hwnd: int, screenshot_path: Optional[str] = None) -> bool:
        """
        检测并点击标签界面的"未刷"选项
        
        Args:
            hwnd: 微信窗口句柄
            screenshot_path: 截图路径，如果为None则自动截图
            
        Returns:
            是否成功检测并点击
        """
        try:
            logger.info("🎯 开始检测标签界面的'未刷'选项")
            
            # 如果没有提供截图，则自动截图
            if not screenshot_path:
                screenshot_path = self.screenshot_handler.capture_window_screenshot_enhanced(hwnd)
                if not screenshot_path:
                    logger.error("无法获取截图")
                    return False
            
            # 获取窗口位置
            window_rect = self.automation.get_window_rect(hwnd)
            if not window_rect:
                logger.error("无法获取窗口位置")
                return False
            
            # 检测"未刷"选项
            unswiped_candidates = self._detect_tag_unswiped_options(screenshot_path, window_rect)
            
            if not unswiped_candidates:
                logger.warning("未检测到'未刷'选项")
                return False
            
            # 选择最佳候选并点击
            best_candidate = unswiped_candidates[0]
            success = self._click_unswiped_candidate(best_candidate, window_rect)
            
            return success
            
        except Exception as e:
            logger.error(f"检测并点击'未刷'选项失败: {e}")
            return False
    
    def _detect_tag_unswiped_options(self, screenshot_path: str, window_rect: Tuple[int, int, int, int]) -> List[Dict]:
        """
        专门检测标签界面的"未刷"选项
        
        Args:
            screenshot_path: 截图路径
            window_rect: 窗口位置
            
        Returns:
            检测到的"未刷"选项列表
        """
        try:
            logger.info(f"📸 分析标签界面截图: {screenshot_path}")
            
            # 读取截图
            image = cv2.imread(screenshot_path)
            if image is None:
                logger.error(f"无法读取截图: {screenshot_path}")
                return []
            
            height, width = image.shape[:2]
            logger.info(f"截图尺寸: {width}x{height}")
            
            # 标签界面的"未刷"选项通常在右侧区域
            detection_region = self._get_tag_detection_region(image)
            
            all_candidates = []
            
            # 方法1: 检测标签界面特有的按钮样式
            button_candidates = self._detect_tag_buttons(detection_region)
            all_candidates.extend(button_candidates)
            logger.info(f"按钮样式检测: {len(button_candidates)} 个候选")
            
            # 方法2: 检测文本区域
            text_candidates = self._detect_text_regions(detection_region)
            all_candidates.extend(text_candidates)
            logger.info(f"文本区域检测: {len(text_candidates)} 个候选")
            
            # 方法3: 检测颜色特征
            color_candidates = self._detect_color_features(detection_region)
            all_candidates.extend(color_candidates)
            logger.info(f"颜色特征检测: {len(color_candidates)} 个候选")
            
            # 方法4: 检测已知位置
            position_candidates = self._detect_known_positions(detection_region)
            all_candidates.extend(position_candidates)
            logger.info(f"已知位置检测: {len(position_candidates)} 个候选")
            
            # 去重和排序
            final_candidates = self._rank_and_filter_candidates(all_candidates, window_rect)
            
            # 保存调试图像
            self._save_debug_image(detection_region, final_candidates, screenshot_path)
            
            logger.info(f"✅ 标签界面'未刷'检测完成，最终候选: {len(final_candidates)} 个")
            
            return final_candidates
            
        except Exception as e:
            logger.error(f"检测标签界面'未刷'选项失败: {e}")
            return []
    
    def _get_tag_detection_region(self, image: np.ndarray) -> np.ndarray:
        """获取标签界面的检测区域"""
        try:
            height, width = image.shape[:2]

            # 根据用户提供的图片，"未刷"选项在左侧列表中
            # 调整检测区域到左侧，覆盖整个标签列表区域
            start_x = 0           # 从左边缘开始
            start_y = 0           # 从顶部开始
            end_x = width // 2    # 到中间位置（覆盖左侧列表）
            end_y = height        # 到底部

            detection_region = image[start_y:end_y, start_x:end_x]

            logger.info(f"标签检测区域: 位置({start_x},{start_y}), 大小({end_x-start_x}x{end_y-start_y})")
            logger.info(f"检测区域调整为左侧列表区域，覆盖'未刷'选项位置")

            return detection_region

        except Exception as e:
            logger.error(f"获取标签检测区域失败: {e}")
            return image
    
    def _detect_tag_buttons(self, region: np.ndarray) -> List[Dict]:
        """检测标签界面的按钮样式"""
        try:
            candidates = []
            
            # 转换为灰度图
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            
            # 使用多种边缘检测
            edges = cv2.Canny(gray, 30, 100)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 标签界面的"未刷"按钮特征
                if (50 <= w <= 150 and 25 <= h <= 60 and 
                    1.5 <= w/h <= 4.0 and area >= 1000):
                    
                    # 检查按钮区域的特征
                    button_roi = region[y:y+h, x:x+w]
                    if button_roi.size > 0:
                        # 计算颜色特征
                        mean_color = np.mean(button_roi, axis=(0, 1))
                        
                        candidate = {
                            'rect': (x, y, w, h),
                            'center': (x + w//2, y + h//2),
                            'confidence': 0.7 + min(0.2, area / 10000),
                            'detection_method': 'tag_button_style',
                            'type': 'unswiped_button',
                            'area': area,
                            'mean_color': mean_color.tolist()
                        }
                        candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"检测标签按钮失败: {e}")
            return []
    
    def _detect_text_regions(self, region: np.ndarray) -> List[Dict]:
        """检测文本区域"""
        try:
            candidates = []

            # 转换为灰度图
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)

            # 专门检测绿色"未刷"文字
            # 转换到HSV颜色空间检测绿色文字
            hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)

            # 绿色文字的HSV范围（根据图片中的绿色"未刷"文字）
            green_lower = np.array([40, 50, 50])   # 绿色下限
            green_upper = np.array([80, 255, 255]) # 绿色上限

            # 创建绿色掩码
            green_mask = cv2.inRange(hsv, green_lower, green_upper)

            # 形态学操作连接文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            green_mask = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)

            # 查找绿色文字轮廓
            contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h

                # "未刷"文字的特征（根据图片分析）
                if (20 <= w <= 80 and 12 <= h <= 30 and
                    1.5 <= w/h <= 4.0 and area >= 200):

                    candidate = {
                        'rect': (x, y, w, h),
                        'center': (x + w//2, y + h//2),
                        'confidence': 0.9,  # 绿色文字检测置信度高
                        'detection_method': 'green_text_unswiped',
                        'type': 'unswiped_text',
                        'area': area
                    }
                    candidates.append(candidate)
                    logger.info(f"检测到绿色'未刷'文字: 位置({x},{y}), 大小({w}x{h}), 面积={area}")

            # 如果没有检测到绿色文字，使用通用文本检测
            if not candidates:
                logger.info("未检测到绿色文字，尝试通用文本检测")

                # 二值化
                _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                # 形态学操作
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
                morph = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

                # 查找轮廓
                contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)

                    # 文本区域特征
                    if (20 <= w <= 80 and 12 <= h <= 30 and
                        1.5 <= w/h <= 4.0):

                        candidate = {
                            'rect': (x, y, w, h),
                            'center': (x + w//2, y + h//2),
                            'confidence': 0.6,
                            'detection_method': 'general_text_region',
                            'type': 'unswiped_text',
                        }
                        candidates.append(candidate)

            return candidates

        except Exception as e:
            logger.error(f"检测文本区域失败: {e}")
            return []
    
    def _detect_color_features(self, region: np.ndarray) -> List[Dict]:
        """检测颜色特征"""
        try:
            candidates = []
            
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
            
            for i, (lower, upper) in enumerate(self.unswiped_colors):
                mask = cv2.inRange(hsv, lower, upper)
                
                # 形态学操作去噪
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    area = w * h
                    
                    if (40 <= w <= 120 and 20 <= h <= 50 and area >= 800):
                        candidate = {
                            'rect': (x, y, w, h),
                            'center': (x + w//2, y + h//2),
                            'confidence': 0.6 + (area / 15000),
                            'detection_method': f'color_feature_{i}',
                            'type': 'unswiped_color',
                            'area': area
                        }
                        candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"检测颜色特征失败: {e}")
            return []
    
    def _detect_known_positions(self, region: np.ndarray) -> List[Dict]:
        """检测已知位置"""
        try:
            candidates = []
            height, width = region.shape[:2]

            # 根据用户图片，"未刷"选项在左侧列表的特定位置
            # 调整已知位置到左侧列表区域
            known_positions = [
                (0.05, 0.15, 0.4, 0.08),  # 左侧上方（图片中"未刷"的大致位置）
                (0.05, 0.25, 0.4, 0.08),  # 左侧中上
                (0.05, 0.35, 0.4, 0.08),  # 左侧中央
                (0.05, 0.45, 0.4, 0.08),  # 左侧中下
                (0.05, 0.55, 0.4, 0.08),  # 左侧下方
                (0.1, 0.2, 0.3, 0.06),    # 更精确的位置1
                (0.1, 0.3, 0.3, 0.06),    # 更精确的位置2
            ]
            
            for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(known_positions):
                x = int(width * x_ratio)
                y = int(height * y_ratio)
                w = int(width * w_ratio)
                h = int(height * h_ratio)
                
                if x + w <= width and y + h <= height:
                    # 分析该区域的特征
                    roi = region[y:y+h, x:x+w]
                    if roi.size > 0:
                        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                        mean_val = float(np.mean(gray_roi))
                        std_val = float(np.std(gray_roi))
                        
                        # 如果区域有一定的对比度，可能是按钮
                        if std_val > 10 and 30 < mean_val < 220:
                            candidate = {
                                'rect': (x, y, w, h),
                                'center': (x + w//2, y + h//2),
                                'confidence': 0.5 + (std_val / 200),
                                'detection_method': f'known_position_{i}',
                                'type': 'unswiped_position',
                                'stats': {'mean': mean_val, 'std': std_val}
                            }
                            candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"检测已知位置失败: {e}")
            return []
    
    def _rank_and_filter_candidates(self, candidates: List[Dict], window_rect: Tuple[int, int, int, int]) -> List[Dict]:
        """排序和过滤候选项"""
        try:
            if not candidates:
                return []
            
            # 计算绝对坐标
            window_left, window_top = window_rect[:2]
            
            for candidate in candidates:
                x, y, w, h = candidate['rect']
                # 注意：这里的坐标是相对于检测区域的，需要加上检测区域的偏移
                detection_offset_x = window_rect[2] // 3  # 检测区域的x偏移
                detection_offset_y = window_rect[3] // 4  # 检测区域的y偏移
                
                abs_x = window_left + detection_offset_x + x
                abs_y = window_top + detection_offset_y + y
                
                candidate['absolute_pos'] = (abs_x, abs_y)
                candidate['absolute_center'] = (abs_x + w//2, abs_y + h//2)
            
            # 去重
            unique_candidates = []
            for candidate in candidates:
                center = candidate['center']
                is_duplicate = False
                
                for existing in unique_candidates:
                    existing_center = existing['center']
                    distance = np.sqrt((center[0] - existing_center[0])**2 + (center[1] - existing_center[1])**2)
                    
                    if distance < 40:  # 距离小于40像素认为是重复
                        if candidate['confidence'] > existing['confidence']:
                            unique_candidates.remove(existing)
                            break
                        else:
                            is_duplicate = True
                            break
                
                if not is_duplicate:
                    unique_candidates.append(candidate)
            
            # 按置信度排序
            unique_candidates.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.info(f"候选项排序: {len(candidates)} -> {len(unique_candidates)} 个")
            
            return unique_candidates
            
        except Exception as e:
            logger.error(f"排序过滤候选项失败: {e}")
            return candidates
    
    def _click_unswiped_candidate(self, candidate: Dict, window_rect: Tuple[int, int, int, int]) -> bool:
        """点击"未刷"候选项"""
        try:
            click_x, click_y = candidate['absolute_center']
            
            logger.info(f"🎯 准备点击'未刷'选项:")
            logger.info(f"   检测方法: {candidate.get('detection_method', 'unknown')}")
            logger.info(f"   置信度: {candidate.get('confidence', 0):.2f}")
            logger.info(f"   点击坐标: ({click_x}, {click_y})")
            
            # 移动鼠标并点击
            pyautogui.moveTo(click_x, click_y, duration=0.5)
            pyautogui.click(click_x, click_y)
            
            logger.info("✅ '未刷'选项点击完成")
            return True
            
        except Exception as e:
            logger.error(f"点击'未刷'候选项失败: {e}")
            return False
    
    def _save_debug_image(self, region: np.ndarray, candidates: List[Dict], original_path: str) -> None:
        """保存调试图像"""
        try:
            if not candidates:
                return
            
            debug_image = region.copy()
            
            # 绘制检测结果
            for i, candidate in enumerate(candidates):
                x, y, w, h = candidate['rect']
                confidence = candidate['confidence']
                method = candidate['detection_method']
                
                # 根据置信度选择颜色
                if confidence > 0.8:
                    color = (0, 255, 0)  # 绿色
                elif confidence > 0.6:
                    color = (0, 255, 255)  # 黄色
                else:
                    color = (0, 0, 255)  # 红色
                
                # 绘制矩形
                cv2.rectangle(debug_image, (x, y), (x+w, y+h), color, 2)
                
                # 绘制标签
                label = f"{i+1}: {confidence:.2f}"
                cv2.putText(debug_image, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 保存调试图像
            debug_path = original_path.replace('.png', '_tag_unswiped_debug.png')
            cv2.imwrite(debug_path, debug_image)
            logger.info(f"标签'未刷'调试图像已保存: {debug_path}")
            
        except Exception as e:
            logger.error(f"保存调试图像失败: {e}")

# 创建全局实例
tag_unswiped_detector = TagUnswipedDetector()

def get_tag_unswiped_detector():
    """获取标签'未刷'检测器实例"""
    return tag_unswiped_detector

if __name__ == "__main__":
    # 测试标签'未刷'检测功能
    print("🧪 测试标签界面'未刷'检测功能")
    
    detector = get_tag_unswiped_detector()
    
    # 查找微信窗口
    automation = WeChatContactsAutomation()
    hwnd = automation.find_wechat_window()
    
    if hwnd:
        print(f"找到微信窗口: {hwnd}")
        success = detector.detect_and_click_unswiped(hwnd)
        print(f"检测结果: {'成功' if success else '失败'}")
    else:
        print("未找到微信窗口")
