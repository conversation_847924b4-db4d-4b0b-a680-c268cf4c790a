# 微信通讯录管理窗口自动化脚本

## 项目简介

这是一个基于Python的自动化脚本，用于激活微信通讯录管理窗口。脚本使用pyautogui库实现全自动化的窗口激活和点击操作，能够自动定位并激活微信应用程序窗口，导航到通讯录管理功能界面。

## 功能特性

- ✅ 自动检测微信进程是否运行
- ✅ 智能查找和激活微信窗口
- ✅ 自适应不同屏幕分辨率和DPI设置
- ✅ 完整的错误处理和重试机制
- ✅ 详细的日志记录和监控
- ✅ 安全的操作中断机制（鼠标移到屏幕左上角可停止）

## 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: Python 3.7+
- **微信**: 已安装微信桌面版

## 安装步骤

### 1. 克隆或下载项目文件

将以下文件保存到同一目录：
- `wechat_contacts_automation.py` - 主脚本文件
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明

### 2. 安装Python依赖

打开命令提示符或PowerShell，导航到项目目录，运行：

```bash
pip install -r requirements.txt
```

或者单独安装各个依赖包：

```bash
pip install pyautogui psutil pywin32 Pillow
```

### 3. 验证安装

运行以下命令验证依赖是否正确安装：

```python
python -c "import pyautogui, psutil, win32gui; print('所有依赖安装成功！')"
```

## 使用方法

### 基本使用

1. **启动微信**: 确保微信桌面版已经启动并登录
2. **运行脚本**: 在项目目录下执行：
   ```bash
   python wechat_contacts_automation.py
   ```
3. **观察执行**: 脚本会自动执行以下步骤：
   - 检测微信进程
   - 查找微信窗口
   - 激活微信窗口
   - 点击通讯录按钮
   - 导航到通讯录管理

### 安全机制

- **紧急停止**: 将鼠标快速移动到屏幕左上角可立即停止脚本执行
- **键盘中断**: 按 `Ctrl+C` 可安全退出脚本

## 配置说明

脚本中的主要配置参数可以根据需要调整：

```python
# 重试配置
self.max_retries = 3        # 最大重试次数
self.retry_delay = 2        # 重试间隔（秒）

# 操作延时配置
self.short_delay = 1        # 短延时（秒）
self.medium_delay = 2       # 中等延时（秒）
self.long_delay = 3         # 长延时（秒）

# pyautogui配置
pyautogui.PAUSE = 0.5       # 每次操作间隔
pyautogui.FAILSAFE = True   # 安全模式开启
```

## 日志文件

脚本运行时会生成详细的日志文件：

- **文件位置**: `wechat_automation.log`
- **日志级别**: INFO, WARNING, ERROR
- **内容包括**: 操作步骤、错误信息、调试数据

## 故障排除

### 常见问题

1. **微信未找到**
   - 确保微信已启动并完全加载
   - 检查微信窗口标题是否匹配脚本中的预设值

2. **点击位置不准确**
   - 调整脚本中的坐标计算比例
   - 确保微信窗口没有被其他窗口遮挡

3. **权限问题**
   - 以管理员身份运行命令提示符
   - 确保Python有足够的系统权限

4. **依赖安装失败**
   - 更新pip: `python -m pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### 调试模式

如需详细调试信息，可以修改日志级别：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 兼容性说明

- **屏幕分辨率**: 自动适配不同分辨率
- **DPI缩放**: 支持Windows DPI缩放设置
- **微信版本**: 兼容主流微信桌面版本

## 注意事项

1. **使用前准备**:
   - 确保微信已登录且界面完全加载
   - 关闭可能干扰的弹窗或通知

2. **执行环境**:
   - 建议在稳定的网络环境下使用
   - 避免在脚本运行时操作鼠标和键盘

3. **安全提醒**:
   - 脚本仅用于合法的自动化操作
   - 请遵守微信的使用条款和相关法律法规

## 技术支持

如遇到问题，请：

1. 查看 `wechat_automation.log` 日志文件
2. 确认系统环境和依赖版本
3. 检查微信版本兼容性

## 版本历史

- **v1.0** (2024): 初始版本，实现基本的微信通讯录管理窗口激活功能

---

**免责声明**: 本脚本仅供学习和个人使用，使用者需自行承担使用风险。
