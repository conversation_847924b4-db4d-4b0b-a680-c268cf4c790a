#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的"未刷"选项检测模块
使用多种图像识别技术精确定位"未刷"选项
"""

import cv2
import numpy as np
import os
import logging
from typing import List, Dict, Optional, Tuple
from PIL import Image
import pytesseract

# 导入配置
try:
    from config import *
except ImportError:
    # 默认配置
    UNSWIPED_KEYWORDS = ["未刷", "未读", "unread", "unswiped"]
    UNSWIPED_DETECTION_REGION_WIDTH = 400
    UNSWIPED_DETECTION_REGION_HEIGHT = 200

logger = logging.getLogger(__name__)

class ImprovedUnswipedDetection:
    """改进的"未刷"选项检测类"""
    
    def __init__(self):
        """初始化检测器"""
        self.unswiped_keywords = getattr(globals(), 'UNSWIPED_KEYWORDS', ["未刷", "未读", "unread", "unswiped"])
        self.detection_region_width = getattr(globals(), 'UNSWIPED_DETECTION_REGION_WIDTH', 400)
        self.detection_region_height = getattr(globals(), 'UNSWIPED_DETECTION_REGION_HEIGHT', 200)
        
        # 检测参数
        self.min_confidence = 0.6
        self.template_match_threshold = 0.7
        self.color_match_threshold = 0.8
        
        logger.info("🔍 改进的未刷选项检测器初始化完成")
    
    def detect_unswiped_options(self, screenshot_path: str, window_rect: Tuple[int, int, int, int]) -> List[Dict]:
        """
        检测"未刷"选项的主要方法
        
        Args:
            screenshot_path: 截图文件路径
            window_rect: 窗口位置 (left, top, right, bottom)
            
        Returns:
            检测到的"未刷"选项列表
        """
        try:
            logger.info(f"🎯 开始检测'未刷'选项: {screenshot_path}")
            
            # 读取截图
            image = cv2.imread(screenshot_path)
            if image is None:
                logger.error(f"无法读取截图文件: {screenshot_path}")
                return []
            
            height, width = image.shape[:2]
            logger.info(f"截图尺寸: {width}x{height}")
            
            # 定义检测区域（通常在窗口的中上部分）
            detection_region = self._get_detection_region(image, width, height)
            
            # 使用多种方法检测"未刷"选项
            all_candidates = []
            
            # 方法1: 基于颜色特征检测
            color_candidates = self._detect_by_color_features(detection_region)
            all_candidates.extend(color_candidates)
            logger.info(f"颜色特征检测到 {len(color_candidates)} 个候选")
            
            # 方法2: 基于形状特征检测
            shape_candidates = self._detect_by_shape_features(detection_region)
            all_candidates.extend(shape_candidates)
            logger.info(f"形状特征检测到 {len(shape_candidates)} 个候选")
            
            # 方法3: 基于文本识别检测
            text_candidates = self._detect_by_text_recognition(detection_region)
            all_candidates.extend(text_candidates)
            logger.info(f"文本识别检测到 {len(text_candidates)} 个候选")
            
            # 方法4: 基于已知位置检测
            position_candidates = self._detect_by_known_positions(detection_region)
            all_candidates.extend(position_candidates)
            logger.info(f"已知位置检测到 {len(position_candidates)} 个候选")
            
            # 去重和排序
            final_candidates = self._deduplicate_and_rank_candidates(all_candidates, window_rect)
            
            logger.info(f"✅ 未刷选项检测完成，最终候选: {len(final_candidates)} 个")
            
            # 保存调试图像
            self._save_debug_image(detection_region, final_candidates, screenshot_path)
            
            return final_candidates
            
        except Exception as e:
            logger.error(f"检测未刷选项失败: {e}")
            return []
    
    def _get_detection_region(self, image: np.ndarray, width: int, height: int) -> np.ndarray:
        """获取检测区域"""
        try:
            # 检测区域通常在窗口的中上部分
            region_width = min(self.detection_region_width, width)
            region_height = min(self.detection_region_height, height)
            
            # 从窗口中心偏左上开始
            start_x = max(0, (width - region_width) // 3)
            start_y = max(0, height // 6)  # 从窗口上方1/6处开始
            
            end_x = min(start_x + region_width, width)
            end_y = min(start_y + region_height, height)
            
            detection_region = image[start_y:end_y, start_x:end_x]
            
            logger.info(f"检测区域: 位置({start_x},{start_y}), 大小({end_x-start_x}x{end_y-start_y})")
            
            return detection_region
            
        except Exception as e:
            logger.error(f"获取检测区域失败: {e}")
            return image
    
    def _detect_by_color_features(self, region: np.ndarray) -> List[Dict]:
        """基于颜色特征检测"""
        try:
            candidates = []
            
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
            
            # 定义"未刷"选项可能的颜色范围
            # 通常是灰色或蓝色背景
            color_ranges = [
                # 灰色范围
                (np.array([0, 0, 100]), np.array([180, 30, 200])),
                # 蓝色范围
                (np.array([100, 50, 50]), np.array([130, 255, 255])),
                # 白色范围
                (np.array([0, 0, 200]), np.array([180, 30, 255]))
            ]
            
            for i, (lower, upper) in enumerate(color_ranges):
                mask = cv2.inRange(hsv, lower, upper)
                
                # 形态学操作去噪
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    area = w * h
                    
                    # 过滤条件：合适的大小和长宽比
                    if (30 <= w <= 120 and 20 <= h <= 50 and 
                        1.5 <= w/h <= 6.0 and area >= 600):
                        
                        candidate = {
                            'rect': (x, y, w, h),
                            'center': (x + w//2, y + h//2),
                            'confidence': 0.7 + (area / 10000),  # 基于面积调整置信度
                            'detection_method': f'color_range_{i}',
                            'type': 'unswiped_option',
                            'area': area
                        }
                        candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"颜色特征检测失败: {e}")
            return []
    
    def _detect_by_shape_features(self, region: np.ndarray) -> List[Dict]:
        """基于形状特征检测"""
        try:
            candidates = []
            
            # 转换为灰度图
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            
            # 使用多种边缘检测方法
            edges_methods = [
                cv2.Canny(gray, 50, 150),
                cv2.Canny(gray, 30, 100),
                cv2.Canny(gray, 70, 200)
            ]
            
            for i, edges in enumerate(edges_methods):
                # 查找轮廓
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    # 计算轮廓属性
                    x, y, w, h = cv2.boundingRect(contour)
                    area = cv2.contourArea(contour)
                    perimeter = cv2.arcLength(contour, True)
                    
                    if perimeter == 0:
                        continue
                    
                    # 计算形状特征
                    aspect_ratio = w / h if h > 0 else 0
                    extent = area / (w * h) if (w * h) > 0 else 0
                    solidity = area / cv2.contourArea(cv2.convexHull(contour)) if cv2.contourArea(cv2.convexHull(contour)) > 0 else 0
                    
                    # 按钮形状特征判断
                    if (40 <= w <= 100 and 25 <= h <= 45 and
                        2.0 <= aspect_ratio <= 4.0 and
                        extent > 0.6 and solidity > 0.8):
                        
                        candidate = {
                            'rect': (x, y, w, h),
                            'center': (x + w//2, y + h//2),
                            'confidence': 0.6 + extent * 0.3,
                            'detection_method': f'shape_edges_{i}',
                            'type': 'unswiped_option',
                            'shape_features': {
                                'aspect_ratio': aspect_ratio,
                                'extent': extent,
                                'solidity': solidity
                            }
                        }
                        candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"形状特征检测失败: {e}")
            return []
    
    def _detect_by_text_recognition(self, region: np.ndarray) -> List[Dict]:
        """基于文本识别检测"""
        try:
            candidates = []
            
            # 转换为灰度图
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            
            # 预处理图像以提高OCR准确性
            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 尝试使用OCR识别文本（如果可用）
            try:
                # 配置OCR参数
                config = '--oem 3 --psm 6 -c tessedit_char_whitelist=未刷未读unreadunswiped'
                
                # 执行OCR
                data = pytesseract.image_to_data(binary, config=config, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')
                
                # 分析OCR结果
                for i in range(len(data['text'])):
                    text = data['text'][i].strip()
                    confidence = int(data['conf'][i])
                    
                    if confidence > 30 and text:  # 置信度阈值
                        # 检查是否包含目标关键词
                        for keyword in self.unswiped_keywords:
                            if keyword in text.lower():
                                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                                
                                if w > 20 and h > 15:  # 最小尺寸过滤
                                    candidate = {
                                        'rect': (x, y, w, h),
                                        'center': (x + w//2, y + h//2),
                                        'confidence': min(0.9, confidence / 100.0 + 0.2),
                                        'detection_method': 'ocr_text',
                                        'type': 'unswiped_option',
                                        'recognized_text': text,
                                        'ocr_confidence': confidence
                                    }
                                    candidates.append(candidate)
                                    logger.info(f"OCR识别到文本: '{text}' (置信度: {confidence})")
                                break
                
            except Exception as ocr_error:
                logger.debug(f"OCR识别失败，跳过文本检测: {ocr_error}")
            
            return candidates
            
        except Exception as e:
            logger.error(f"文本识别检测失败: {e}")
            return []
    
    def _detect_by_known_positions(self, region: np.ndarray) -> List[Dict]:
        """基于已知位置检测"""
        try:
            candidates = []
            height, width = region.shape[:2]
            
            # 基于微信界面布局的"未刷"选项可能位置
            known_positions = [
                (0.25, 0.3, 0.2, 0.15),   # 中心偏左位置 (x%, y%, w%, h%)
                (0.3, 0.25, 0.25, 0.2),   # 中心位置
                (0.2, 0.35, 0.3, 0.18),   # 左侧中央
                (0.35, 0.3, 0.2, 0.15),   # 右侧中央
            ]
            
            for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(known_positions):
                x = int(width * x_ratio)
                y = int(height * y_ratio)
                w = int(width * w_ratio)
                h = int(height * h_ratio)
                
                # 确保位置在区域内
                if x + w <= width and y + h <= height:
                    # 分析该区域的图像特征
                    roi = region[y:y+h, x:x+w]
                    if roi.size > 0:
                        # 计算区域特征
                        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                        mean_val = np.mean(gray_roi)
                        std_val = np.std(gray_roi)
                        
                        # 如果区域有一定的对比度，认为可能是按钮
                        if std_val > 15 and 50 < mean_val < 200:
                            candidate = {
                                'rect': (x, y, w, h),
                                'center': (x + w//2, y + h//2),
                                'confidence': 0.5 + (std_val / 100),
                                'detection_method': f'known_position_{i}',
                                'type': 'unswiped_option',
                                'image_stats': {'mean': mean_val, 'std': std_val}
                            }
                            candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"已知位置检测失败: {e}")
            return []
    
    def _deduplicate_and_rank_candidates(self, candidates: List[Dict], window_rect: Tuple[int, int, int, int]) -> List[Dict]:
        """去重和排序候选项"""
        try:
            if not candidates:
                return []
            
            # 计算绝对坐标
            window_left, window_top = window_rect[:2]
            
            for candidate in candidates:
                x, y, w, h = candidate['rect']
                abs_x = window_left + x
                abs_y = window_top + y
                candidate['absolute_pos'] = (abs_x, abs_y)
                candidate['absolute_center'] = (abs_x + w//2, abs_y + h//2)
            
            # 去重：移除位置过于接近的候选项
            unique_candidates = []
            for candidate in candidates:
                center = candidate['center']
                is_duplicate = False
                
                for existing in unique_candidates:
                    existing_center = existing['center']
                    distance = np.sqrt((center[0] - existing_center[0])**2 + (center[1] - existing_center[1])**2)
                    
                    if distance < 30:  # 距离小于30像素认为是重复
                        # 保留置信度更高的
                        if candidate['confidence'] > existing['confidence']:
                            unique_candidates.remove(existing)
                            break
                        else:
                            is_duplicate = True
                            break
                
                if not is_duplicate:
                    unique_candidates.append(candidate)
            
            # 按置信度排序
            unique_candidates.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.info(f"去重排序: {len(candidates)} -> {len(unique_candidates)} 个候选")
            
            return unique_candidates
            
        except Exception as e:
            logger.error(f"去重排序失败: {e}")
            return candidates
    
    def _save_debug_image(self, region: np.ndarray, candidates: List[Dict], original_path: str) -> None:
        """保存调试图像"""
        try:
            if not candidates:
                return
            
            debug_image = region.copy()
            
            # 绘制检测结果
            for i, candidate in enumerate(candidates):
                x, y, w, h = candidate['rect']
                confidence = candidate['confidence']
                method = candidate['detection_method']
                
                # 根据置信度选择颜色
                if confidence > 0.8:
                    color = (0, 255, 0)  # 绿色 - 高置信度
                elif confidence > 0.6:
                    color = (0, 255, 255)  # 黄色 - 中等置信度
                else:
                    color = (0, 0, 255)  # 红色 - 低置信度
                
                # 绘制矩形
                cv2.rectangle(debug_image, (x, y), (x+w, y+h), color, 2)
                
                # 绘制标签
                label = f"{i+1}: {confidence:.2f} ({method})"
                cv2.putText(debug_image, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 保存调试图像
            debug_path = original_path.replace('.png', '_unswiped_debug.png')
            cv2.imwrite(debug_path, debug_image)
            logger.info(f"调试图像已保存: {debug_path}")
            
        except Exception as e:
            logger.error(f"保存调试图像失败: {e}")

# 创建全局实例
improved_unswiped_detector = ImprovedUnswipedDetection()

def get_improved_unswiped_detector():
    """获取改进的未刷检测器实例"""
    return improved_unswiped_detector

if __name__ == "__main__":
    # 测试改进的未刷检测功能
    print("🧪 测试改进的未刷检测功能")
    
    # 这里可以添加测试代码
    detector = get_improved_unswiped_detector()
    print("✅ 改进的未刷检测器初始化完成")
