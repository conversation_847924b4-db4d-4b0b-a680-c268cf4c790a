#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信通讯录自动化复合操作模块
实现"标签"+"未刷"连续点击功能
"""

import cv2
import numpy as np
import pyautogui
import time
import os
from typing import List, Dict, Optional
from datetime import datetime

# 导入配置
from config import *

def find_unswiped_elements(automation_instance, hwnd: int, window_elements: Dict) -> List[Dict]:
    """
    查找"未刷"选项元素
    
    Args:
        automation_instance: WeChatContactsAutomation实例
        hwnd: 窗口句柄
        window_elements: 窗口元素信息
        
    Returns:
        未刷选项元素列表
    """
    try:
        automation_instance.logger.info("🔍 开始查找'未刷'选项元素")
        
        # 获取窗口截图
        screenshot_path = window_elements.get('screenshot_path')
        if not screenshot_path or not os.path.exists(screenshot_path):
            automation_instance.logger.error("窗口截图不存在，无法检测未刷选项")
            return []
        
        # 读取截图
        image = cv2.imread(screenshot_path)
        if image is None:
            automation_instance.logger.error("无法读取窗口截图")
            return []
        
        height, width = image.shape[:2]
        automation_instance.logger.info(f"分析截图尺寸: {width}x{height}")
        
        # 定义检测区域（通常在窗口的中上部分）
        detection_width = min(UNSWIPED_DETECTION_REGION_WIDTH, width)
        detection_height = min(UNSWIPED_DETECTION_REGION_HEIGHT, height)
        
        # 检测区域从窗口中心偏左开始
        start_x = max(0, (width - detection_width) // 3)
        start_y = max(0, height // 6)  # 从窗口上方1/6处开始
        
        detection_region = image[start_y:start_y+detection_height, start_x:start_x+detection_width]
        automation_instance.logger.info(f"未刷选项检测区域: 位置({start_x},{start_y}), 大小({detection_width}x{detection_height})")
        
        # 使用多种方法检测"未刷"选项
        unswiped_elements = []
        
        # 方法1: 基于已知位置的检测
        unswiped_elements.extend(detect_unswiped_by_position(automation_instance, detection_region, start_x, start_y, hwnd))
        
        # 方法2: 基于文本特征的检测
        unswiped_elements.extend(detect_unswiped_by_text_features(automation_instance, detection_region, start_x, start_y, hwnd))
        
        # 去重和排序
        unswiped_elements = deduplicate_and_sort_unswiped_elements(automation_instance, unswiped_elements)
        
        automation_instance.logger.info(f"✅ 未刷选项检测完成，共找到 {len(unswiped_elements)} 个候选元素")
        
        return unswiped_elements
        
    except Exception as e:
        automation_instance.logger.error(f"查找未刷选项元素失败: {e}")
        return []

def detect_unswiped_by_position(automation_instance, detection_region: np.ndarray, 
                               start_x: int, start_y: int, hwnd: int) -> List[Dict]:
    """
    基于已知位置检测"未刷"选项
    """
    try:
        automation_instance.logger.info("📍 使用位置特征检测'未刷'选项")
        
        # 获取窗口信息用于坐标计算
        window_rect = automation_instance.get_window_rect(hwnd)
        if not window_rect:
            return []
        
        window_left, window_top = window_rect[:2]
        
        # 基于微信界面布局的"未刷"选项可能位置
        # 通常在标签界面的中上部分
        known_unswiped_positions = [
            (100, 80, 60, 30),   # 中心偏左位置
            (120, 80, 60, 30),   # 稍微右移
            (100, 100, 60, 30),  # 稍微下移
            (120, 100, 60, 30),  # 右下位置
            (80, 80, 80, 30),    # 更宽的区域
        ]
        
        unswiped_elements = []
        
        for i, (x, y, w, h) in enumerate(known_unswiped_positions):
            # 确保位置在检测区域内
            if x + w <= detection_region.shape[1] and y + h <= detection_region.shape[0]:
                # 计算绝对坐标
                abs_x = window_left + start_x + x
                abs_y = window_top + start_y + y
                abs_center = (abs_x + w//2, abs_y + h//2)
                
                element = {
                    'rect': (start_x + x, start_y + y, w, h),
                    'center': (start_x + x + w//2, start_y + y + h//2),
                    'absolute_pos': (abs_x, abs_y),
                    'absolute_center': abs_center,
                    'type': 'unswiped_option',
                    'unswiped_type': 'position_based',
                    'confidence': 0.75,
                    'priority': 2,
                    'detection_method': 'known_position'
                }
                
                unswiped_elements.append(element)
                automation_instance.logger.info(f"📍 添加已知位置候选 {i+1}: 位置=({x},{y}), 屏幕坐标=({abs_x},{abs_y})")
        
        return unswiped_elements
        
    except Exception as e:
        automation_instance.logger.error(f"位置检测未刷选项失败: {e}")
        return []

def detect_unswiped_by_text_features(automation_instance, detection_region: np.ndarray,
                                    start_x: int, start_y: int, hwnd: int) -> List[Dict]:
    """
    基于文本特征检测"未刷"选项
    """
    try:
        automation_instance.logger.info("🔤 使用文本特征检测'未刷'选项")
        
        # 获取窗口信息
        window_rect = automation_instance.get_window_rect(hwnd)
        if not window_rect:
            return []
        
        window_left, window_top = window_rect[:2]
        
        # 转换为灰度图像进行文本检测
        gray = cv2.cvtColor(detection_region, cv2.COLOR_BGR2GRAY)
        height, width = gray.shape
        
        unswiped_elements = []
        
        # 在检测区域中寻找可能的文本区域
        for y in range(0, height - UNSWIPED_MIN_HEIGHT, 10):
            for x in range(0, width - UNSWIPED_MIN_WIDTH, 10):
                w = min(UNSWIPED_MAX_WIDTH, width - x)
                h = min(UNSWIPED_MAX_HEIGHT, height - y)
                
                if w >= UNSWIPED_MIN_WIDTH and h >= UNSWIPED_MIN_HEIGHT:
                    # 检查这个区域是否有文本特征
                    roi = gray[y:y+h, x:x+w]
                    
                    if roi.size > 0:
                        # 计算文本特征
                        mean_val = float(roi.mean())
                        std_val = float(roi.std())
                        
                        # 文本区域通常有一定的对比度
                        if std_val > 10 and 40 < mean_val < 220:
                            # 计算绝对坐标
                            abs_x = window_left + start_x + x
                            abs_y = window_top + start_y + y
                            abs_center = (abs_x + w//2, abs_y + h//2)
                            
                            element = {
                                'rect': (start_x + x, start_y + y, w, h),
                                'center': (start_x + x + w//2, start_y + y + h//2),
                                'absolute_pos': (abs_x, abs_y),
                                'absolute_center': abs_center,
                                'type': 'unswiped_option',
                                'unswiped_type': 'text_based',
                                'confidence': 0.70,
                                'priority': 3,
                                'detection_method': 'text_features',
                                'text_stats': {'mean': mean_val, 'std': std_val}
                            }
                            
                            unswiped_elements.append(element)
        
        automation_instance.logger.info(f"📝 文本特征检测完成，找到 {len(unswiped_elements)} 个候选")
        return unswiped_elements
        
    except Exception as e:
        automation_instance.logger.error(f"文本特征检测未刷选项失败: {e}")
        return []

def deduplicate_and_sort_unswiped_elements(automation_instance, unswiped_elements: List[Dict]) -> List[Dict]:
    """
    去重和排序未刷选项元素
    """
    try:
        if not unswiped_elements:
            return []
        
        # 简单去重：移除位置过于接近的元素
        unique_elements = []
        for element in unswiped_elements:
            center = element['center']
            is_duplicate = False
            
            for existing in unique_elements:
                existing_center = existing['center']
                distance = ((center[0] - existing_center[0])**2 + (center[1] - existing_center[1])**2)**0.5
                if distance < 20:  # 距离小于20像素认为是重复
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_elements.append(element)
        
        # 按优先级和置信度排序
        unique_elements.sort(key=lambda x: (x.get('priority', 5), -x.get('confidence', 0)))
        
        automation_instance.logger.info(f"去重排序完成: {len(unswiped_elements)} -> {len(unique_elements)}")
        return unique_elements
        
    except Exception as e:
        automation_instance.logger.error(f"去重排序未刷元素失败: {e}")
        return unswiped_elements

def click_unswiped_element(automation_instance, hwnd: int, unswiped_elements: List[Dict]) -> bool:
    """
    点击"未刷"选项元素
    """
    try:
        if not unswiped_elements:
            automation_instance.logger.error("❌ 没有可点击的未刷选项元素")
            return False
        
        best_element = unswiped_elements[0]  # 已按优先级排序
        
        # 获取点击坐标
        if 'absolute_center' in best_element:
            click_x, click_y = best_element['absolute_center']
        else:
            abs_x, abs_y = best_element['absolute_pos']
            w, h = best_element['rect'][2], best_element['rect'][3]
            click_x = abs_x + w // 2
            click_y = abs_y + h // 2
        
        automation_instance.logger.info(f"📊 选择的未刷选项详情:")
        automation_instance.logger.info(f"   类型: {best_element.get('unswiped_type', 'unknown')}")
        automation_instance.logger.info(f"   置信度: {best_element.get('confidence', 0):.2f}")
        automation_instance.logger.info(f"   位置: {best_element['rect']}")
        automation_instance.logger.info(f"   点击坐标: ({click_x}, {click_y})")
        
        # 移动鼠标并点击
        automation_instance.logger.info(f"🖱️ 移动鼠标到未刷选项: ({click_x}, {click_y})")
        pyautogui.moveTo(click_x, click_y, duration=0.5)
        time.sleep(automation_instance.short_delay)
        
        automation_instance.logger.info(f"🖱️ 点击未刷选项: ({click_x}, {click_y})")
        pyautogui.click(click_x, click_y, duration=0.1)
        
        automation_instance.logger.info("✅ 未刷选项点击操作完成")
        time.sleep(automation_instance.medium_delay)
        
        return True
        
    except Exception as e:
        automation_instance.logger.error(f"❌ 点击未刷选项失败: {e}")
        return False

def click_tag_and_unswiped(automation_instance, hwnd: int) -> bool:
    """
    复合操作：点击"标签"菜单项，然后点击"未刷"选项
    
    Args:
        automation_instance: WeChatContactsAutomation实例
        hwnd: 窗口句柄
        
    Returns:
        操作是否成功
    """
    try:
        if not automation_instance.compound_operation_enabled:
            automation_instance.logger.warning("复合操作功能已禁用")
            return False
            
        automation_instance.logger.info("🎯 开始执行复合操作：标签 → 未刷")
        
        # 第一步：点击"标签"菜单项
        automation_instance.logger.info("📋 步骤1: 点击'标签'菜单项")
        tag_click_success = automation_instance.execute_tag_operation(hwnd, "CLICK", skip_activation=True)
        
        if not tag_click_success:
            automation_instance.logger.error("❌ 第一步失败：无法点击标签菜单项")
            return False
        
        automation_instance.logger.info("✅ 第一步成功：标签菜单项点击完成")
        
        # 等待界面加载
        automation_instance.logger.info(f"⏳ 等待界面加载 {automation_instance.tag_to_unswiped_delay} 秒...")
        time.sleep(automation_instance.tag_to_unswiped_delay)
        
        # 第二步：检测并点击"未刷"选项
        automation_instance.logger.info("📋 步骤2: 检测并点击'未刷'选项")
        
        # 重新截图获取最新界面
        screenshot_path = automation_instance.capture_window_screenshot(hwnd, skip_activation=True)
        if not screenshot_path:
            automation_instance.logger.error("❌ 无法获取更新后的窗口截图")
            return False
        
        # 分析窗口元素
        window_elements = automation_instance.analyze_window_elements(hwnd, screenshot_path)
        if not window_elements:
            automation_instance.logger.error("❌ 无法分析更新后的窗口元素")
            return False
        
        # 查找"未刷"选项
        unswiped_elements = find_unswiped_elements(automation_instance, hwnd, window_elements)
        if not unswiped_elements:
            automation_instance.logger.warning("⚠️ 未找到'未刷'选项，可能界面未正确加载")
            return False
        
        # 点击"未刷"选项
        unswiped_click_success = click_unswiped_element(automation_instance, hwnd, unswiped_elements)
        
        if unswiped_click_success:
            automation_instance.logger.info("✅ 第二步成功：未刷选项点击完成")
            automation_instance.logger.info("🎉 复合操作完全成功：标签 → 未刷")
            return True
        else:
            automation_instance.logger.error("❌ 第二步失败：无法点击未刷选项")
            automation_instance.logger.info("⚠️ 复合操作部分成功：标签点击成功，但未刷点击失败")
            return False
            
    except Exception as e:
        automation_instance.logger.error(f"复合操作失败: {e}")
        return False

# 为WeChatContactsAutomation类动态添加复合操作方法
def add_compound_operations_to_automation_class():
    """
    将复合操作方法动态添加到WeChatContactsAutomation类中
    """
    from wechat_contacts_automation import WeChatContactsAutomation
    
    # 添加方法到类中
    WeChatContactsAutomation.find_unswiped_elements = lambda self, hwnd, window_elements: find_unswiped_elements(self, hwnd, window_elements)
    WeChatContactsAutomation.click_tag_and_unswiped = lambda self, hwnd: click_tag_and_unswiped(self, hwnd)
    
    print("✅ 复合操作功能已添加到WeChatContactsAutomation类中")

if __name__ == "__main__":
    # 测试复合操作功能
    add_compound_operations_to_automation_class()
    
    from wechat_contacts_automation import WeChatContactsAutomation
    
    automation = WeChatContactsAutomation()
    hwnd = automation.find_wechat_window()
    
    if hwnd:
        print("🚀 测试复合操作：标签 → 未刷")
        success = automation.click_tag_and_unswiped(hwnd)
        print(f"复合操作结果: {'成功' if success else '失败'}")
    else:
        print("❌ 未找到微信窗口")
