#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证微信自动化功能
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_simple_logging():
    """设置简单的日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试基础依赖
        import pyautogui
        import cv2
        import numpy as np
        from PIL import Image
        import win32gui
        print("   ✅ 基础依赖导入成功")
        
        # 测试自定义模块
        from enhanced_screenshot import get_enhanced_screenshot_instance
        from improved_unswiped_detection import get_improved_unswiped_detector
        from complete_automation_flow import get_complete_automation_instance
        print("   ✅ 自定义模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False

def test_wechat_detection():
    """测试微信窗口检测"""
    print("\n🔍 测试微信窗口检测...")
    
    try:
        from wechat_contacts_automation import WeChatContactsAutomation
        
        automation = WeChatContactsAutomation()
        
        # 检查微信是否运行
        if not automation.is_wechat_running():
            print("   ⚠️ 微信未运行，请先启动微信")
            return False
        
        # 查找微信窗口
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("   ❌ 未找到微信窗口")
            return False
        
        # 获取窗口信息
        import win32gui
        window_title = win32gui.GetWindowText(hwnd)
        window_rect = automation.get_window_rect(hwnd)
        
        print(f"   ✅ 找到微信窗口: {window_title}")
        print(f"   📐 窗口位置: {window_rect}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 微信窗口检测失败: {e}")
        return False

def test_screenshot_function():
    """测试截图功能"""
    print("\n📸 测试截图功能...")
    
    try:
        from enhanced_screenshot import get_enhanced_screenshot_instance
        from wechat_contacts_automation import WeChatContactsAutomation
        
        automation = WeChatContactsAutomation()
        screenshot_handler = get_enhanced_screenshot_instance()
        
        # 查找微信窗口
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("   ❌ 未找到微信窗口")
            return False
        
        # 测试截图
        screenshot_path = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
        
        if screenshot_path and os.path.exists(screenshot_path):
            print(f"   ✅ 截图成功: {screenshot_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(screenshot_path)
            print(f"   📊 文件大小: {file_size} 字节")
            
            return True
        else:
            print("   ❌ 截图失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 截图功能测试失败: {e}")
        return False

def test_detection_function():
    """测试检测功能"""
    print("\n🎯 测试'未刷'检测功能...")
    
    try:
        from improved_unswiped_detection import get_improved_unswiped_detector
        from wechat_contacts_automation import WeChatContactsAutomation
        
        automation = WeChatContactsAutomation()
        detector = get_improved_unswiped_detector()
        
        # 查找微信窗口
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("   ❌ 未找到微信窗口")
            return False
        
        # 获取最新截图
        screenshots_dir = "screenshots"
        if not os.path.exists(screenshots_dir):
            print("   ⚠️ 截图目录不存在，跳过检测测试")
            return True
        
        # 查找最新的截图文件
        import glob
        screenshot_files = glob.glob(os.path.join(screenshots_dir, "*.png"))
        if not screenshot_files:
            print("   ⚠️ 没有找到截图文件，跳过检测测试")
            return True
        
        latest_screenshot = max(screenshot_files, key=os.path.getmtime)
        print(f"   📁 使用截图: {latest_screenshot}")
        
        # 获取窗口位置
        window_rect = automation.get_window_rect(hwnd)
        if not window_rect:
            print("   ❌ 无法获取窗口位置")
            return False
        
        # 执行检测
        candidates = detector.detect_unswiped_options(latest_screenshot, window_rect)
        
        print(f"   🔍 检测结果: 找到 {len(candidates)} 个候选")
        
        for i, candidate in enumerate(candidates[:3]):  # 只显示前3个
            confidence = candidate.get('confidence', 0)
            method = candidate.get('detection_method', 'unknown')
            rect = candidate.get('rect', (0, 0, 0, 0))
            print(f"     候选 {i+1}: 置信度={confidence:.2f}, 方法={method}, 位置={rect}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 检测功能测试失败: {e}")
        return False

def test_complete_flow():
    """测试完整流程（不实际执行点击）"""
    print("\n🚀 测试完整流程组件...")
    
    try:
        from complete_automation_flow import get_complete_automation_instance
        
        automation = get_complete_automation_instance()
        print("   ✅ 完整流程实例创建成功")
        
        # 测试各个组件是否正常
        print("   🔧 检查组件状态:")
        print(f"     - 自动化实例: {'✅' if automation.automation else '❌'}")
        print(f"     - 截图处理器: {'✅' if automation.screenshot_handler else '❌'}")
        print(f"     - 检测器: {'✅' if automation.unswiped_detector else '❌'}")
        
        # 检查配置
        print("   ⚙️ 配置参数:")
        print(f"     - 等待时间: {automation.wait_time} 秒")
        print(f"     - 最大重试: {automation.max_retries} 次")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 完整流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 微信自动化功能快速测试")
    print("=" * 50)
    
    setup_simple_logging()
    
    # 执行各项测试
    tests = [
        ("模块导入", test_imports),
        ("微信检测", test_wechat_detection),
        ("截图功能", test_screenshot_function),
        ("检测功能", test_detection_function),
        ("完整流程", test_complete_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n💡 现在可以运行完整的自动化程序:")
        print("   python wechat_tag_unswiped_automation.py --mode interactive")
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
        print("\n🔧 建议:")
        print("   1. 确保微信正在运行")
        print("   2. 检查依赖包是否完整安装")
        print("   3. 查看详细错误信息")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    input("按回车键退出...")
    
    sys.exit(0 if success else 1)
