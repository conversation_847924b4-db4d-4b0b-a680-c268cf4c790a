#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信通讯录自动化脚本配置文件
用户可以根据自己的需求修改这些参数
"""

# 微信窗口标题配置（支持多种可能的窗口标题）
WECHAT_WINDOW_TITLES = [
    "微信",
    "WeChat", 
    "微信 (WeChat)",
    "WeChat for Windows",
    "微信 for Windows",
    "WeChat App",
    "微信应用"
]

# 通讯录相关窗口标题配置
CONTACTS_WINDOW_TITLES = [
    "通讯录",
    "联系人", 
    "contacts",
    "address book",
    "管理",
    "manage",
    "编辑",
    "edit",
    "好友",
    "friends",
    "群组",
    "groups"
]

# 标签相关配置
TAG_RELATED_KEYWORDS = [
    "标签",
    "tag",
    "tags",
    "标记",
    "mark",
    "分类",
    "category",
    "分组",
    "group",
    "label",
    "labels"
]

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 2

# 操作延时配置（秒）
SHORT_DELAY = 1
MEDIUM_DELAY = 2
LONG_DELAY = 3

# PyAutoGUI配置
PYAUTOGUI_PAUSE = 0.5
PYAUTOGUI_FAILSAFE = True

# 窗口操作配置（相对坐标比例）
CONTACTS_BUTTON_X_RATIO = 0.0
CONTACTS_BUTTON_Y_RATIO = 0.25
CONTACTS_AREA_X_RATIO = 0.5
CONTACTS_AREA_Y_RATIO = 0.5
MANAGE_BUTTON_X_RATIO = 0.85
MANAGE_BUTTON_Y_RATIO = 0.15

# 标签操作配置（相对坐标比例）
TAG_BUTTON_X_RATIO = 0.2
TAG_BUTTON_Y_RATIO = 0.3
TAG_AREA_X_RATIO = 0.6
TAG_AREA_Y_RATIO = 0.4
TAG_EDIT_X_RATIO = 0.7
TAG_EDIT_Y_RATIO = 0.5
TAG_ADD_BUTTON_X_RATIO = 0.8
TAG_ADD_BUTTON_Y_RATIO = 0.2
TAG_DELETE_BUTTON_X_RATIO = 0.9
TAG_DELETE_BUTTON_Y_RATIO = 0.3

# 鼠标操作配置
MOUSE_MOVE_DURATION = 0.5
CLICK_DURATION = 0.1
DOUBLE_CLICK_INTERVAL = 0.2

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "wechat_automation.log"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"

# 窗口检测配置
PRIORITIZE_CONTACTS_WINDOWS = True
CONTACTS_WINDOW_SCORE_BONUS = 500
AUTO_DETECT_CONTACTS_INTERFACE = True

# 截图配置
SCREENSHOT_DIR = "screenshots"
SCREENSHOT_FORMAT = "PNG"
SCREENSHOT_QUALITY = 95
SAVE_ANNOTATED_SCREENSHOTS = True
SCREENSHOT_TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"

# 截图清理配置
MAX_SCREENSHOTS_TO_KEEP = 5  # 最多保留的截图数量
AUTO_CLEANUP_SCREENSHOTS = True  # 是否自动清理旧截图
CLEANUP_ON_STARTUP = True  # 是否在程序启动时清理

# 元素识别配置
ELEMENT_DETECTION_CONFIDENCE = 0.8
ELEMENT_SEARCH_TIMEOUT = 10
ELEMENT_CLICK_OFFSET_X = 0
ELEMENT_CLICK_OFFSET_Y = 0

# 标签操作配置
TAG_OPERATION_TIMEOUT = 15
TAG_SEARCH_CONFIDENCE = 0.7
TAG_TEXT_INPUT_DELAY = 0.1
TAG_OPERATION_VERIFY_DELAY = 2

# 复合操作配置 - "标签"+"未刷"连续点击
COMPOUND_OPERATION_ENABLED = True  # 是否启用复合操作
TAG_TO_UNSWIPED_DELAY = 3.0  # 点击标签后等待界面加载的时间（秒）
UNSWIPED_DETECTION_TIMEOUT = 10  # 未刷选项检测超时时间（秒）
UNSWIPED_CLICK_CONFIDENCE = 0.8  # 未刷选项点击置信度阈值

# "未刷"选项检测配置
UNSWIPED_KEYWORDS = ["未刷", "未读", "unread", "unswiped"]  # 未刷选项关键词
UNSWIPED_DETECTION_REGION_WIDTH = 400  # 未刷选项检测区域宽度
UNSWIPED_DETECTION_REGION_HEIGHT = 200  # 未刷选项检测区域高度
UNSWIPED_MIN_WIDTH = 30  # 未刷选项最小宽度
UNSWIPED_MAX_WIDTH = 120  # 未刷选项最大宽度
UNSWIPED_MIN_HEIGHT = 20  # 未刷选项最小高度
UNSWIPED_MAX_HEIGHT = 50  # 未刷选项最大高度

# ==================== 左侧标签区域检测配置 ====================

# 左侧区域分析参数
LEFT_SIDEBAR_WIDTH = 200  # 左侧分析区域宽度（像素）
LEFT_SIDEBAR_PRIORITY = True  # 是否优先使用左侧区域检测

# 轮廓检测参数（针对左侧区域优化）
LEFT_CANNY_LOW_THRESHOLD = 20   # Canny边缘检测低阈值
LEFT_CANNY_HIGH_THRESHOLD = 80  # Canny边缘检测高阈值
LEFT_ADAPTIVE_BLOCK_SIZE = 9    # 自适应阈值块大小
LEFT_ADAPTIVE_C = 2             # 自适应阈值常数

# 标签元素识别参数
TAG_BUTTON_MIN_WIDTH = 15       # 标签按钮最小宽度
TAG_BUTTON_MAX_WIDTH = 180      # 标签按钮最大宽度
TAG_BUTTON_MIN_HEIGHT = 20      # 标签按钮最小高度
TAG_BUTTON_MAX_HEIGHT = 60      # 标签按钮最大高度
TAG_BUTTON_MIN_ASPECT_RATIO = 1.0   # 标签按钮最小长宽比
TAG_BUTTON_MAX_ASPECT_RATIO = 6.0   # 标签按钮最大长宽比

TAG_ICON_MIN_SIZE = 10          # 标签图标最小尺寸
TAG_ICON_MAX_SIZE = 40          # 标签图标最大尺寸
TAG_ICON_MIN_ASPECT_RATIO = 0.7 # 标签图标最小长宽比
TAG_ICON_MAX_ASPECT_RATIO = 1.4 # 标签图标最大长宽比

TAG_TEXT_MIN_WIDTH = 20         # 标签文本最小宽度
TAG_TEXT_MAX_WIDTH = 150        # 标签文本最大宽度
TAG_TEXT_MIN_HEIGHT = 12        # 标签文本最小高度
TAG_TEXT_MAX_HEIGHT = 35        # 标签文本最大高度
TAG_TEXT_MIN_ASPECT_RATIO = 1.5 # 标签文本最小长宽比

# 置信度配置
TAG_BUTTON_BASE_CONFIDENCE = 0.9    # 标签按钮基础置信度
TAG_ICON_BASE_CONFIDENCE = 0.8      # 标签图标基础置信度
TAG_TEXT_BASE_CONFIDENCE = 0.7      # 标签文本基础置信度
CLICKABLE_BASE_CONFIDENCE = 0.6     # 可点击元素基础置信度
LEFT_REGION_CONFIDENCE_BONUS = 0.3  # 左侧区域置信度加分

# 调试配置
SAVE_LEFT_SIDEBAR_DEBUG = True     # 保存左侧区域调试截图
DETAILED_COORDINATE_LOGGING = True # 详细坐标计算日志

# 标注颜色配置（BGR格式）
ANNOTATION_COLORS = {
    'tag_button': (0, 255, 0),      # 绿色 - 标签按钮
    'tag_icon': (255, 0, 0),        # 蓝色 - 标签图标
    'tag_text': (0, 255, 255),      # 黄色 - 标签文本
    'clickable': (255, 0, 255),     # 紫色 - 可点击元素
    'center_point': (0, 0, 255),    # 红色 - 中心点
    'boundary': (255, 255, 255)     # 白色 - 边界线
}

# 左侧区域预设位置（相对坐标，比例）
LEFT_PRESET_POSITIONS = [
    (0.05, 0.2, 0.15, 0.05),   # 左上区域 (x%, y%, w%, h%)
    (0.03, 0.3, 0.18, 0.06),   # 左侧中上
    (0.04, 0.4, 0.16, 0.05),   # 左侧中央
    (0.06, 0.5, 0.14, 0.04),   # 左侧中下
]

# 图像识别配置
IMAGE_MATCH_CONFIDENCE = 0.8
IMAGE_SEARCH_REGION_PADDING = 50
USE_GRAYSCALE_MATCHING = True

# 错误处理配置
MAX_SCREENSHOT_RETRIES = 3
SCREENSHOT_RETRY_DELAY = 1
OPERATION_FAILURE_SCREENSHOT = True

# 标签操作类型定义
TAG_OPERATIONS = {
    "CLICK": "click_tag",
    "EDIT": "edit_tag", 
    "ADD": "add_tag",
    "DELETE": "delete_tag",
    "VIEW": "view_tags",
    "SEARCH": "search_tag"
}

# 默认标签操作参数
DEFAULT_TAG_OPERATION = "VIEW"
DEFAULT_TAG_TEXT = ""
DEFAULT_TAG_POSITION = "auto"

# 验证配置
VERIFY_OPERATION_SUCCESS = True
VERIFICATION_TIMEOUT = 5
VERIFICATION_SCREENSHOT = True

# 调试配置
DEBUG_MODE = False
VERBOSE_LOGGING = False
SAVE_DEBUG_SCREENSHOTS = True
DEBUG_SCREENSHOT_PREFIX = "debug_"

# 性能配置
OPERATION_TIMEOUT = 30
WINDOW_ACTIVATION_TIMEOUT = 10
ELEMENT_WAIT_TIMEOUT = 5

# 安全配置
CONFIRM_DESTRUCTIVE_OPERATIONS = True
BACKUP_BEFORE_OPERATIONS = False
SAFE_MODE = False

# 高级配置
USE_ADVANCED_ELEMENT_DETECTION = True
ENABLE_OCR_FALLBACK = False
OCR_LANGUAGE = "chi_sim"
OCR_CONFIDENCE_THRESHOLD = 0.6

# 坐标计算配置
COORDINATE_PRECISION = 1
BOUNDARY_SAFETY_MARGIN = 10
ADAPTIVE_COORDINATE_ADJUSTMENT = True

# 多显示器支持
PRIMARY_MONITOR_ONLY = True
MONITOR_DETECTION_ENABLED = False

# 兼容性配置
WINDOWS_VERSION_COMPATIBILITY = "auto"
DPI_AWARENESS = True
HIGH_DPI_SCALING = "auto"

# 实验性功能
EXPERIMENTAL_FEATURES = False
MACHINE_LEARNING_ELEMENT_DETECTION = False
SMART_RETRY_LOGIC = True

# 用户界面配置
SHOW_PROGRESS_INDICATORS = True
DISPLAY_OPERATION_FEEDBACK = True
MINIMIZE_CONSOLE_OUTPUT = False

# 网络配置（如果需要在线功能）
ENABLE_ONLINE_FEATURES = False
API_TIMEOUT = 10
RETRY_NETWORK_OPERATIONS = True

# 文件路径配置
TEMP_DIR = "temp"
BACKUP_DIR = "backup"
LOG_DIR = "logs"
CONFIG_BACKUP_ENABLED = True

# 多语言支持
LANGUAGE = "zh_CN"
FALLBACK_LANGUAGE = "en_US"
AUTO_DETECT_LANGUAGE = True

# 性能监控
ENABLE_PERFORMANCE_MONITORING = False
PERFORMANCE_LOG_FILE = "performance.log"
MEMORY_USAGE_MONITORING = False

# 自动化流程配置
AUTO_RECOVERY_ENABLED = True
SMART_WAIT_ENABLED = True
ADAPTIVE_TIMING = True

# 扩展功能配置
PLUGIN_SUPPORT = False
CUSTOM_SCRIPTS_DIR = "scripts"
HOOK_SUPPORT = False

# ==================== 增强功能配置 ====================

# 增强截图功能配置
ENHANCED_SCREENSHOT_ENABLED = True
SCREENSHOT_METHODS = ["pyautogui", "pil", "win32"]  # 支持的截图方法
SCREENSHOT_METHOD_AUTO_FALLBACK = True  # 自动回退到其他方法
SCREENSHOT_QUALITY_VALIDATION = True  # 启用截图质量验证
SCREENSHOT_MIN_VARIANCE = 100  # 最小颜色方差阈值
SCREENSHOT_MIN_SIZE = (100, 100)  # 最小截图尺寸

# 改进的"未刷"检测配置
IMPROVED_UNSWIPED_DETECTION_ENABLED = True
UNSWIPED_DETECTION_METHODS = ["color", "shape", "text", "position"]  # 检测方法
UNSWIPED_MIN_CONFIDENCE = 0.6  # 最小置信度阈值
UNSWIPED_TEMPLATE_MATCH_THRESHOLD = 0.7  # 模板匹配阈值
UNSWIPED_COLOR_MATCH_THRESHOLD = 0.8  # 颜色匹配阈值
UNSWIPED_OCR_ENABLED = True  # 启用OCR文本识别
UNSWIPED_OCR_LANGUAGES = ["chi_sim", "eng"]  # OCR支持的语言

# 完整自动化流程配置
COMPLETE_AUTOMATION_ENABLED = True
SMART_WAIT_ENABLED = True  # 启用智能等待
SMART_WAIT_CHECK_INTERVAL = 0.5  # 智能等待检查间隔（秒）
PROGRESS_FEEDBACK_ENABLED = True  # 启用进度反馈
DETAILED_STEP_LOGGING = True  # 详细步骤日志

# 流程步骤配置
STEP_1_WINDOW_ACTIVATION_TIMEOUT = 10  # 窗口激活超时
STEP_2_TAG_CLICK_TIMEOUT = 5  # 标签点击超时
STEP_3_INTERFACE_LOAD_WAIT = 3.0  # 界面加载等待时间
STEP_4_SCREENSHOT_TIMEOUT = 10  # 截图超时
STEP_5_DETECTION_TIMEOUT = 15  # 检测超时
STEP_6_CLICK_TIMEOUT = 5  # 点击超时

# 错误恢复配置
AUTO_RECOVERY_ENABLED = True
RECOVERY_SCREENSHOT_ON_FAILURE = True  # 失败时保存截图
RECOVERY_MAX_ATTEMPTS = 3  # 最大恢复尝试次数
RECOVERY_WAIT_BETWEEN_ATTEMPTS = 2  # 恢复尝试间隔

# 调试和诊断配置
SAVE_DEBUG_SCREENSHOTS = True  # 保存调试截图
DEBUG_SCREENSHOT_PREFIX = "debug_"  # 调试截图前缀
SAVE_DETECTION_RESULTS = True  # 保存检测结果
DETECTION_RESULT_FORMAT = "json"  # 检测结果格式

# 性能优化配置
PARALLEL_DETECTION_ENABLED = False  # 并行检测（实验性）
CACHE_DETECTION_RESULTS = True  # 缓存检测结果
CACHE_EXPIRY_TIME = 300  # 缓存过期时间（秒）
OPTIMIZE_IMAGE_PROCESSING = True  # 优化图像处理
