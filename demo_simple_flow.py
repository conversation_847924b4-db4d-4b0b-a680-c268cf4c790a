#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化演示版本 - 微信界面自动化操作
专注于展示核心功能，便于理解和调试
"""

import sys
import os
import time
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from enhanced_screenshot import get_enhanced_screenshot_instance
from wechat_contacts_automation import WeChatContactsAutomation

def setup_logging():
    """设置简单日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def demo_step_by_step():
    """分步演示每个功能"""
    print("🎯 微信界面自动化 - 分步演示")
    print("=" * 50)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化组件
        automation = WeChatContactsAutomation()
        screenshot_handler = get_enhanced_screenshot_instance()
        
        print("\n📋 步骤1: 查找微信窗口")
        input("请确保微信正在运行，然后按回车继续...")
        
        # 查找微信窗口
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("❌ 未找到微信窗口")
            return False
        
        print(f"✅ 找到微信窗口，句柄: {hwnd}")
        
        print("\n📋 步骤2: 激活微信窗口")
        success = automation.activate_window(hwnd)
        if success:
            print("✅ 微信窗口激活成功")
        else:
            print("⚠️ 窗口激活失败，但继续执行")
        
        print("\n📋 步骤3: 截图当前界面")
        screenshot_path = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
        if screenshot_path:
            print(f"✅ 截图成功: {screenshot_path}")
            print(f"📁 可以查看截图文件了解当前界面状态")
        else:
            print("❌ 截图失败")
            return False
        
        print("\n📋 步骤4: 点击标签菜单（如果存在）")
        user_input = input("是否尝试点击标签菜单? (y/N): ").strip().lower()
        
        if user_input in ['y', 'yes']:
            print("🖱️ 尝试点击标签菜单...")
            try:
                tag_success = automation.execute_tag_operation(hwnd, "CLICK", skip_activation=True)
                if tag_success:
                    print("✅ 标签菜单点击成功")
                    
                    print("\n📋 步骤5: 等待界面加载")
                    wait_time = 3.0
                    print(f"⏳ 等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                    
                    print("\n📋 步骤6: 重新截图")
                    new_screenshot = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
                    if new_screenshot:
                        print(f"✅ 新截图保存: {new_screenshot}")
                        print("📊 可以对比前后截图查看界面变化")
                    
                else:
                    print("⚠️ 标签菜单点击可能失败，但这是正常的")
                    print("💡 这可能是因为当前界面没有标签菜单，或者位置需要调整")
                    
            except Exception as e:
                print(f"⚠️ 标签操作异常: {e}")
        
        print("\n📋 步骤7: 清理旧截图")
        cleanup_success = screenshot_handler.cleanup_old_screenshots(keep_count=3)
        if cleanup_success:
            print("✅ 截图清理完成")
        
        print("\n🎉 演示完成！")
        print("\n📊 总结:")
        print("   ✅ 微信窗口检测和激活")
        print("   ✅ 增强截图功能")
        print("   ✅ 标签菜单点击尝试")
        print("   ✅ 智能等待机制")
        print("   ✅ 自动截图清理")
        
        return True
        
    except Exception as e:
        logger.error(f"演示过程出错: {e}")
        return False

def demo_screenshot_analysis():
    """演示截图分析功能"""
    print("\n🔍 截图分析演示")
    print("-" * 30)
    
    # 查看最新的截图
    screenshots_dir = "screenshots"
    if os.path.exists(screenshots_dir):
        import glob
        screenshot_files = glob.glob(os.path.join(screenshots_dir, "*.png"))
        if screenshot_files:
            latest_screenshot = max(screenshot_files, key=os.path.getmtime)
            print(f"📁 最新截图: {latest_screenshot}")
            
            # 显示文件信息
            file_size = os.path.getsize(latest_screenshot)
            mod_time = datetime.fromtimestamp(os.path.getmtime(latest_screenshot))
            print(f"📊 文件大小: {file_size} 字节")
            print(f"🕒 修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 如果有标注截图，也显示
            annotated_files = [f for f in screenshot_files if "annotated" in f]
            if annotated_files:
                latest_annotated = max(annotated_files, key=os.path.getmtime)
                print(f"🎨 最新标注截图: {latest_annotated}")
        else:
            print("📁 截图目录为空")
    else:
        print("📁 截图目录不存在")

def main():
    """主函数"""
    print("🚀 微信界面自动化 - 简化演示版")
    print("版本: 2.0 Enhanced Demo")
    print("=" * 50)
    
    try:
        # 检查基本环境
        print("🔧 环境检查:")
        
        try:
            import pyautogui
            import cv2
            import win32gui
            from PIL import Image
            print("   ✅ 所有依赖模块正常")
        except ImportError as e:
            print(f"   ❌ 缺少依赖: {e}")
            return 1
        
        # 选择演示模式
        print("\n🎮 选择演示模式:")
        print("   1. 完整分步演示")
        print("   2. 仅截图分析")
        print("   3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            success = demo_step_by_step()
            demo_screenshot_analysis()
            return 0 if success else 1
            
        elif choice == "2":
            demo_screenshot_analysis()
            return 0
            
        elif choice == "3":
            print("👋 再见！")
            return 0
            
        else:
            print("❌ 无效选择")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n" + "=" * 50)
    if exit_code == 0:
        print("✅ 演示完成")
        print("\n💡 接下来可以:")
        print("   1. 查看 screenshots/ 目录中的截图文件")
        print("   2. 运行完整版: python wechat_tag_unswiped_automation.py")
        print("   3. 查看日志文件了解详细执行过程")
    else:
        print("❌ 演示失败")
        print("\n🔧 故障排除:")
        print("   1. 确保微信正在运行")
        print("   2. 检查依赖包安装")
        print("   3. 查看错误信息")
    
    input("\n按回车键退出...")
    sys.exit(exit_code)
