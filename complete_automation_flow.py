#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的微信界面自动化操作流程
实现：标签点击 → 等待 → 截图 → 分析 → 点击"未刷"
"""

import time
import logging
import pyautogui
import win32gui
from typing import Optional, Dict, List
from datetime import datetime

# 导入自定义模块
from enhanced_screenshot import get_enhanced_screenshot_instance
from improved_unswiped_detection import get_improved_unswiped_detector
from wechat_contacts_automation import WeChatContactsAutomation

# 导入配置
try:
    from config import *
except ImportError:
    # 默认配置
    TAG_TO_UNSWIPED_DELAY = 3.0
    MAX_RETRIES = 3
    RETRY_DELAY = 2

logger = logging.getLogger(__name__)

class CompleteAutomationFlow:
    """完整的自动化流程类"""
    
    def __init__(self):
        """初始化自动化流程"""
        self.automation = WeChatContactsAutomation()
        self.screenshot_handler = get_enhanced_screenshot_instance()
        self.unswiped_detector = get_improved_unswiped_detector()
        
        # 配置参数
        self.wait_time = getattr(globals(), 'TAG_TO_UNSWIPED_DELAY', 3.0)
        self.max_retries = getattr(globals(), 'MAX_RETRIES', 3)
        self.retry_delay = getattr(globals(), 'RETRY_DELAY', 2)
        
        logger.info("🚀 完整自动化流程初始化完成")
    
    def execute_complete_flow(self, custom_wait_time: Optional[float] = None) -> bool:
        """
        执行完整的自动化流程
        
        Args:
            custom_wait_time: 自定义等待时间，如果为None则使用配置值
            
        Returns:
            操作是否成功
        """
        try:
            wait_time = custom_wait_time if custom_wait_time is not None else self.wait_time
            
            logger.info("🎯 开始执行完整的微信界面自动化流程")
            logger.info(f"📋 流程步骤: 标签点击 → 等待({wait_time}s) → 截图分析 → 点击未刷")
            
            # 第一步：查找并激活微信窗口
            logger.info("📋 步骤1: 查找并激活微信窗口")
            hwnd = self._find_and_activate_wechat_window()
            if not hwnd:
                logger.error("❌ 步骤1失败：无法找到或激活微信窗口")
                return False
            
            logger.info("✅ 步骤1成功：微信窗口已激活")
            
            # 第二步：点击"标签"菜单项
            logger.info("📋 步骤2: 点击'标签'菜单项")
            tag_click_success = self._click_tag_menu(hwnd)
            if not tag_click_success:
                logger.error("❌ 步骤2失败：无法点击标签菜单项")
                return False
            
            logger.info("✅ 步骤2成功：标签菜单项点击完成")
            
            # 第三步：智能等待界面加载
            logger.info(f"📋 步骤3: 智能等待界面加载 ({wait_time} 秒)")
            self._smart_wait_for_interface(wait_time)
            logger.info("✅ 步骤3成功：界面加载等待完成")
            
            # 第四步：删除旧截图并重新截图
            logger.info("📋 步骤4: 删除旧截图并重新截图分析")
            screenshot_path = self._capture_fresh_screenshot(hwnd)
            if not screenshot_path:
                logger.error("❌ 步骤4失败：无法获取新的界面截图")
                return False
            
            logger.info(f"✅ 步骤4成功：新截图已保存 - {screenshot_path}")
            
            # 第五步：分析截图，检测"未刷"选项
            logger.info("📋 步骤5: 分析截图，检测'未刷'选项")
            unswiped_options = self._detect_unswiped_options(screenshot_path, hwnd)
            if not unswiped_options:
                logger.error("❌ 步骤5失败：未检测到'未刷'选项")
                return False
            
            logger.info(f"✅ 步骤5成功：检测到 {len(unswiped_options)} 个'未刷'选项候选")
            
            # 第六步：精确点击"未刷"选项
            logger.info("📋 步骤6: 精确点击'未刷'选项")
            click_success = self._click_unswiped_option(unswiped_options)
            if not click_success:
                logger.error("❌ 步骤6失败：无法点击'未刷'选项")
                return False
            
            logger.info("✅ 步骤6成功：'未刷'选项点击完成")
            
            logger.info("🎉 完整自动化流程执行成功！")
            return True
            
        except Exception as e:
            logger.error(f"完整自动化流程执行失败: {e}")
            return False
    
    def _find_and_activate_wechat_window(self) -> Optional[int]:
        """查找并激活微信窗口"""
        try:
            # 检查微信是否运行
            if not self.automation.is_wechat_running():
                logger.error("微信未运行，请先启动微信应用程序")
                return None
            
            # 查找微信窗口
            hwnd = self.automation.find_wechat_window()
            if not hwnd:
                logger.error("无法找到微信窗口")
                return None
            
            # 激活微信窗口
            if not self.automation.activate_window(hwnd):
                logger.error("无法激活微信窗口")
                return None
            
            return hwnd
            
        except Exception as e:
            logger.error(f"查找激活微信窗口失败: {e}")
            return None
    
    def _click_tag_menu(self, hwnd: int) -> bool:
        """点击标签菜单项"""
        try:
            # 使用现有的标签操作功能
            success = self.automation.execute_tag_operation(hwnd, "CLICK", skip_activation=True)
            
            if success:
                logger.info("标签菜单项点击成功")
                return True
            else:
                logger.warning("标签菜单项点击失败，尝试备选方法")
                
                # 备选方法：基于窗口位置计算标签按钮位置
                window_rect = self.automation.get_window_rect(hwnd)
                if window_rect:
                    return self._click_tag_by_position(window_rect)
                
                return False
                
        except Exception as e:
            logger.error(f"点击标签菜单失败: {e}")
            return False
    
    def _click_tag_by_position(self, window_rect) -> bool:
        """基于位置点击标签按钮（备选方法）"""
        try:
            left, top, right, bottom = window_rect
            
            # 标签按钮通常在左侧导航栏
            tag_x = left + 40  # 左侧导航栏中央
            tag_y = top + 180  # 大约在第三个位置
            
            logger.info(f"尝试点击标签按钮位置: ({tag_x}, {tag_y})")
            
            pyautogui.moveTo(tag_x, tag_y, duration=0.5)
            time.sleep(0.5)
            pyautogui.click(tag_x, tag_y)
            
            return True
            
        except Exception as e:
            logger.error(f"基于位置点击标签失败: {e}")
            return False
    
    def _smart_wait_for_interface(self, wait_time: float) -> None:
        """智能等待界面加载"""
        try:
            logger.info(f"⏳ 开始智能等待，预计时间: {wait_time} 秒")
            
            # 分段等待，每0.5秒检查一次
            total_waited = 0.0
            check_interval = 0.5
            
            while total_waited < wait_time:
                time.sleep(check_interval)
                total_waited += check_interval
                
                # 显示进度
                progress = (total_waited / wait_time) * 100
                logger.debug(f"等待进度: {progress:.1f}% ({total_waited:.1f}s/{wait_time}s)")
            
            logger.info(f"✅ 智能等待完成，总等待时间: {total_waited:.1f} 秒")
            
        except Exception as e:
            logger.error(f"智能等待失败: {e}")
            # 即使等待失败，也继续执行后续步骤
    
    def _capture_fresh_screenshot(self, hwnd: int) -> Optional[str]:
        """删除旧截图并捕获新截图"""
        try:
            logger.info("🗑️ 清理旧截图文件...")
            
            # 清理旧截图并捕获新截图
            screenshot_path = self.screenshot_handler.capture_and_cleanup(hwnd, cleanup_first=True)
            
            if screenshot_path:
                logger.info(f"📸 新截图已保存: {screenshot_path}")
                return screenshot_path
            else:
                logger.error("截图捕获失败")
                return None
                
        except Exception as e:
            logger.error(f"捕获新截图失败: {e}")
            return None
    
    def _detect_unswiped_options(self, screenshot_path: str, hwnd: int) -> List[Dict]:
        """检测"未刷"选项"""
        try:
            # 获取窗口位置信息
            window_rect = self.automation.get_window_rect(hwnd)
            if not window_rect:
                logger.error("无法获取窗口位置信息")
                return []

            # 首先尝试使用专门的标签"未刷"检测器
            try:
                from tag_unswiped_detector import get_tag_unswiped_detector
                tag_detector = get_tag_unswiped_detector()
                unswiped_options = tag_detector._detect_tag_unswiped_options(screenshot_path, window_rect)

                if unswiped_options:
                    logger.info(f"🎯 标签专用检测器找到 {len(unswiped_options)} 个'未刷'选项候选:")
                    for i, option in enumerate(unswiped_options):
                        confidence = option.get('confidence', 0)
                        method = option.get('detection_method', 'unknown')
                        rect = option.get('rect', (0, 0, 0, 0))
                        logger.info(f"  候选 {i+1}: 置信度={confidence:.2f}, 方法={method}, 位置={rect}")
                    return unswiped_options
                else:
                    logger.info("标签专用检测器未找到候选，尝试通用检测器")
            except Exception as e:
                logger.warning(f"标签专用检测器失败，使用通用检测器: {e}")

            # 如果专用检测器失败，使用原有的通用检测器
            unswiped_options = self.unswiped_detector.detect_unswiped_options(screenshot_path, window_rect)

            if unswiped_options:
                logger.info(f"🎯 通用检测器找到 {len(unswiped_options)} 个'未刷'选项候选:")
                for i, option in enumerate(unswiped_options):
                    confidence = option.get('confidence', 0)
                    method = option.get('detection_method', 'unknown')
                    rect = option.get('rect', (0, 0, 0, 0))
                    logger.info(f"  候选 {i+1}: 置信度={confidence:.2f}, 方法={method}, 位置={rect}")

            return unswiped_options

        except Exception as e:
            logger.error(f"检测未刷选项失败: {e}")
            return []
    
    def _click_unswiped_option(self, unswiped_options: List[Dict]) -> bool:
        """点击"未刷"选项"""
        try:
            if not unswiped_options:
                logger.error("没有可点击的'未刷'选项")
                return False
            
            # 选择置信度最高的选项
            best_option = unswiped_options[0]  # 已按置信度排序
            
            # 获取点击坐标
            if 'absolute_center' in best_option:
                click_x, click_y = best_option['absolute_center']
            else:
                abs_pos = best_option.get('absolute_pos', (0, 0))
                rect = best_option.get('rect', (0, 0, 0, 0))
                click_x = abs_pos[0] + rect[2] // 2
                click_y = abs_pos[1] + rect[3] // 2
            
            logger.info(f"🎯 选择最佳'未刷'选项:")
            logger.info(f"   置信度: {best_option.get('confidence', 0):.2f}")
            logger.info(f"   检测方法: {best_option.get('detection_method', 'unknown')}")
            logger.info(f"   点击坐标: ({click_x}, {click_y})")
            
            # 执行点击操作
            logger.info(f"🖱️ 移动鼠标到目标位置: ({click_x}, {click_y})")
            pyautogui.moveTo(click_x, click_y, duration=0.5)
            time.sleep(0.5)
            
            logger.info(f"🖱️ 点击'未刷'选项: ({click_x}, {click_y})")
            pyautogui.click(click_x, click_y)
            
            # 等待点击响应
            time.sleep(1.0)
            
            logger.info("✅ '未刷'选项点击操作完成")
            return True
            
        except Exception as e:
            logger.error(f"点击'未刷'选项失败: {e}")
            return False
    
    def execute_with_retry(self, max_retries: Optional[int] = None, 
                          custom_wait_time: Optional[float] = None) -> bool:
        """
        带重试机制的完整流程执行
        
        Args:
            max_retries: 最大重试次数
            custom_wait_time: 自定义等待时间
            
        Returns:
            操作是否成功
        """
        try:
            retries = max_retries if max_retries is not None else self.max_retries
            
            logger.info(f"🔄 开始执行带重试的完整流程，最大重试次数: {retries}")
            
            for attempt in range(retries + 1):
                if attempt > 0:
                    logger.info(f"🔄 第 {attempt} 次重试...")
                    time.sleep(self.retry_delay)
                
                success = self.execute_complete_flow(custom_wait_time)
                
                if success:
                    if attempt == 0:
                        logger.info("🎉 首次尝试即成功完成完整流程！")
                    else:
                        logger.info(f"🎉 第 {attempt} 次重试成功完成完整流程！")
                    return True
                else:
                    if attempt < retries:
                        logger.warning(f"⚠️ 第 {attempt + 1} 次尝试失败，准备重试...")
                    else:
                        logger.error(f"❌ 所有尝试都失败了，共尝试 {retries + 1} 次")
            
            return False
            
        except Exception as e:
            logger.error(f"带重试的完整流程执行失败: {e}")
            return False

# 创建全局实例
complete_automation = CompleteAutomationFlow()

def get_complete_automation_instance():
    """获取完整自动化流程实例"""
    return complete_automation

def execute_wechat_tag_to_unswiped_flow(wait_time: float = 3.0, max_retries: int = 3) -> bool:
    """
    执行微信标签到未刷的完整流程（便捷函数）
    
    Args:
        wait_time: 等待时间（秒）
        max_retries: 最大重试次数
        
    Returns:
        操作是否成功
    """
    automation = get_complete_automation_instance()
    return automation.execute_with_retry(max_retries, wait_time)

if __name__ == "__main__":
    # 测试完整自动化流程
    print("🧪 测试完整的微信界面自动化流程")
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 执行测试
    success = execute_wechat_tag_to_unswiped_flow(wait_time=3.0, max_retries=2)
    
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 完整的微信界面自动化流程测试通过！")
        print("📋 流程包括：标签点击 → 等待加载 → 截图分析 → 点击未刷")
    else:
        print("⚠️ 流程测试失败，请检查:")
        print("   1. 微信是否正在运行")
        print("   2. 微信窗口是否可见")
        print("   3. 是否在正确的界面")
