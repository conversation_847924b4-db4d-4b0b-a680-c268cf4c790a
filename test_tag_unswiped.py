#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标签界面"未刷"检测功能
专门用于调试和优化"未刷"选项检测
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tag_unswiped_detector import get_tag_unswiped_detector
from wechat_contacts_automation import WeChatContactsAutomation
from enhanced_screenshot import get_enhanced_screenshot_instance

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def test_tag_unswiped_detection():
    """测试标签"未刷"检测功能"""
    print("🧪 测试标签界面'未刷'检测功能")
    print("=" * 50)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化组件
        automation = WeChatContactsAutomation()
        screenshot_handler = get_enhanced_screenshot_instance()
        tag_detector = get_tag_unswiped_detector()
        
        print("\n📋 步骤1: 查找微信窗口")
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("❌ 未找到微信窗口")
            return False
        
        print(f"✅ 找到微信窗口: {hwnd}")
        
        print("\n📋 步骤2: 激活微信窗口")
        automation.activate_window(hwnd)
        
        print("\n📋 步骤3: 截图当前界面")
        screenshot_path = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
        if not screenshot_path:
            print("❌ 截图失败")
            return False
        
        print(f"✅ 截图成功: {screenshot_path}")
        
        print("\n📋 步骤4: 分析当前界面状态")
        # 检查是否在标签界面
        window_rect = automation.get_window_rect(hwnd)
        print(f"窗口位置: {window_rect}")
        
        print("\n📋 步骤5: 使用专用检测器检测'未刷'选项")
        unswiped_candidates = tag_detector._detect_tag_unswiped_options(screenshot_path, window_rect)
        
        if unswiped_candidates:
            print(f"✅ 检测到 {len(unswiped_candidates)} 个'未刷'选项候选:")
            for i, candidate in enumerate(unswiped_candidates):
                confidence = candidate.get('confidence', 0)
                method = candidate.get('detection_method', 'unknown')
                rect = candidate.get('rect', (0, 0, 0, 0))
                abs_pos = candidate.get('absolute_center', (0, 0))
                print(f"  候选 {i+1}:")
                print(f"    检测方法: {method}")
                print(f"    置信度: {confidence:.2f}")
                print(f"    位置: {rect}")
                print(f"    绝对坐标: {abs_pos}")
            
            print("\n📋 步骤6: 选择最佳候选进行点击测试")
            user_input = input("是否点击最佳候选? (y/N): ").strip().lower()
            
            if user_input in ['y', 'yes']:
                best_candidate = unswiped_candidates[0]
                print(f"🎯 准备点击最佳候选: 置信度={best_candidate.get('confidence', 0):.2f}")
                
                success = tag_detector._click_unswiped_candidate(best_candidate, window_rect)
                if success:
                    print("✅ 点击操作完成")
                    
                    # 等待一下，然后重新截图验证
                    import time
                    time.sleep(2)
                    
                    print("\n📋 步骤7: 验证点击结果")
                    verify_screenshot = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
                    if verify_screenshot:
                        print(f"✅ 验证截图保存: {verify_screenshot}")
                        print("📊 可以对比前后截图查看点击效果")
                else:
                    print("❌ 点击操作失败")
            else:
                print("跳过点击测试")
        else:
            print("❌ 未检测到'未刷'选项")
            print("\n🔧 可能的原因:")
            print("   1. 当前不在标签界面")
            print("   2. 界面中没有'未刷'选项")
            print("   3. 检测算法需要调整")
            
            print("\n💡 建议:")
            print("   1. 确保已点击标签菜单")
            print("   2. 检查是否有未读的标签")
            print("   3. 查看调试截图了解检测过程")
        
        print("\n📊 调试信息:")
        print(f"   截图文件: {screenshot_path}")
        debug_path = screenshot_path.replace('.png', '_tag_unswiped_debug.png')
        if os.path.exists(debug_path):
            print(f"   调试图像: {debug_path}")
        
        return len(unswiped_candidates) > 0
        
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        return False

def analyze_current_interface():
    """分析当前界面状态"""
    print("\n🔍 分析当前界面状态")
    print("-" * 30)
    
    try:
        automation = WeChatContactsAutomation()
        screenshot_handler = get_enhanced_screenshot_instance()
        
        hwnd = automation.find_wechat_window()
        if not hwnd:
            print("❌ 未找到微信窗口")
            return
        
        # 截图并分析
        screenshot_path = screenshot_handler.capture_window_screenshot_enhanced(hwnd)
        if screenshot_path:
            print(f"📸 当前界面截图: {screenshot_path}")
            
            # 显示文件信息
            file_size = os.path.getsize(screenshot_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(screenshot_path))
            print(f"📊 文件大小: {file_size} 字节")
            print(f"🕒 截图时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 简单的界面分析
            import cv2
            image = cv2.imread(screenshot_path)
            if image is not None:
                height, width = image.shape[:2]
                print(f"📐 图像尺寸: {width}x{height}")
                
                # 分析颜色分布
                mean_color = cv2.mean(image)
                print(f"🎨 平均颜色: B={mean_color[0]:.1f}, G={mean_color[1]:.1f}, R={mean_color[2]:.1f}")
        
    except Exception as e:
        print(f"❌ 界面分析失败: {e}")

def main():
    """主函数"""
    print("🎯 标签界面'未刷'检测测试工具")
    print("版本: 1.0")
    print("=" * 50)
    
    try:
        print("\n🎮 选择测试模式:")
        print("   1. 完整检测测试")
        print("   2. 界面状态分析")
        print("   3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            success = test_tag_unswiped_detection()
            return 0 if success else 1
            
        elif choice == "2":
            analyze_current_interface()
            return 0
            
        elif choice == "3":
            print("👋 再见！")
            return 0
            
        else:
            print("❌ 无效选择")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n" + "=" * 50)
    if exit_code == 0:
        print("✅ 测试完成")
        print("\n💡 接下来可以:")
        print("   1. 查看调试截图了解检测过程")
        print("   2. 根据结果调整检测参数")
        print("   3. 运行完整的自动化流程")
    else:
        print("❌ 测试失败")
        print("\n🔧 故障排除:")
        print("   1. 确保微信正在运行")
        print("   2. 确保在标签界面")
        print("   3. 检查是否有未读标签")
    
    input("\n按回车键退出...")
    sys.exit(exit_code)
