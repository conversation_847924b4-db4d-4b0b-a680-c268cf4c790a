#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信标签到未刷选项自动化操作主程序
完整实现：点击标签 → 等待 → 截图 → 分析 → 点击未刷
"""

import sys
import os
import logging
import argparse
from datetime import datetime
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from complete_automation_flow import execute_wechat_tag_to_unswiped_flow, get_complete_automation_instance
from enhanced_screenshot import get_enhanced_screenshot_instance
from improved_unswiped_detection import get_improved_unswiped_detector

# 导入配置
try:
    from config import *
except ImportError:
    print("⚠️ 配置文件未找到，使用默认配置")

def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    try:
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 生成日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"wechat_automation_{timestamp}.log")
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
        
    except Exception as e:
        print(f"日志设置失败: {e}")
        # 使用基本日志配置
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    微信界面自动化操作程序                      ║
║                                                              ║
║  功能: 自动点击标签菜单 → 等待加载 → 截图分析 → 点击"未刷"     ║
║  版本: 2.0 Enhanced                                          ║
║  作者: AI Assistant                                          ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_system_info():
    """打印系统信息"""
    logger = logging.getLogger(__name__)
    
    print("\n📊 系统信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    
    # 检查依赖模块
    dependencies = [
        ("pyautogui", "界面自动化"),
        ("cv2", "图像处理"),
        ("PIL", "图像处理"),
        ("numpy", "数值计算"),
        ("win32gui", "Windows API"),
        ("pytesseract", "OCR文字识别")
    ]
    
    print("\n📦 依赖检查:")
    missing_deps = []
    
    for module_name, description in dependencies:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name} - {description}")
        except ImportError:
            print(f"   ❌ {module_name} - {description} (未安装)")
            missing_deps.append(module_name)
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def run_interactive_mode():
    """交互模式"""
    logger = logging.getLogger(__name__)
    
    print("\n🎮 进入交互模式")
    print("请根据提示进行操作...")
    
    try:
        # 获取用户输入
        print("\n⚙️ 配置参数:")
        
        # 等待时间配置
        default_wait = getattr(globals(), 'TAG_TO_UNSWIPED_DELAY', 3.0)
        wait_input = input(f"等待时间 (默认 {default_wait} 秒): ").strip()
        wait_time = float(wait_input) if wait_input else default_wait
        
        # 重试次数配置
        default_retries = getattr(globals(), 'MAX_RETRIES', 3)
        retry_input = input(f"最大重试次数 (默认 {default_retries}): ").strip()
        max_retries = int(retry_input) if retry_input else default_retries
        
        print(f"\n📋 配置确认:")
        print(f"   等待时间: {wait_time} 秒")
        print(f"   最大重试: {max_retries} 次")
        
        confirm = input("\n确认执行? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("操作已取消")
            return False
        
        # 执行自动化流程
        print("\n🚀 开始执行自动化流程...")
        success = execute_wechat_tag_to_unswiped_flow(wait_time, max_retries)
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        logger.error(f"交互模式执行失败: {e}")
        return False

def run_batch_mode(wait_time: float, max_retries: int):
    """批处理模式"""
    logger = logging.getLogger(__name__)
    
    print(f"\n🤖 批处理模式执行")
    print(f"   等待时间: {wait_time} 秒")
    print(f"   最大重试: {max_retries} 次")
    
    try:
        success = execute_wechat_tag_to_unswiped_flow(wait_time, max_retries)
        return success
        
    except Exception as e:
        logger.error(f"批处理模式执行失败: {e}")
        return False

def run_test_mode():
    """测试模式"""
    logger = logging.getLogger(__name__)
    
    print("\n🧪 测试模式")
    print("正在测试各个组件...")
    
    try:
        # 测试截图功能
        print("\n📸 测试增强截图功能...")
        screenshot_handler = get_enhanced_screenshot_instance()
        print("   ✅ 截图模块加载成功")
        
        # 测试检测功能
        print("\n🔍 测试改进的未刷检测功能...")
        detector = get_improved_unswiped_detector()
        print("   ✅ 检测模块加载成功")
        
        # 测试完整流程
        print("\n🎯 测试完整自动化流程...")
        automation = get_complete_automation_instance()
        print("   ✅ 自动化流程模块加载成功")
        
        print("\n✅ 所有组件测试通过！")
        
        # 询问是否执行实际测试
        confirm = input("\n是否执行实际的微信自动化测试? (y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            print("\n🚀 开始实际测试...")
            success = execute_wechat_tag_to_unswiped_flow(wait_time=2.0, max_retries=1)
            return success
        else:
            print("跳过实际测试")
            return True
            
    except Exception as e:
        logger.error(f"测试模式执行失败: {e}")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="微信界面自动化操作程序")
    parser.add_argument("--mode", choices=["interactive", "batch", "test"], 
                       default="interactive", help="运行模式")
    parser.add_argument("--wait", type=float, default=3.0, 
                       help="等待时间（秒）")
    parser.add_argument("--retries", type=int, default=3, 
                       help="最大重试次数")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="日志级别")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # 打印横幅
    print_banner()
    
    # 检查系统环境
    if not print_system_info():
        print("\n❌ 系统环境检查失败，程序退出")
        return 1
    
    # 根据模式执行
    success = False
    
    try:
        if args.mode == "interactive":
            success = run_interactive_mode()
        elif args.mode == "batch":
            success = run_batch_mode(args.wait, args.retries)
        elif args.mode == "test":
            success = run_test_mode()
        
        # 输出结果
        print("\n" + "="*60)
        if success:
            print("🎉 程序执行成功！")
            print("✅ 微信界面自动化操作已完成")
            print("📋 操作流程: 标签点击 → 等待加载 → 截图分析 → 点击未刷")
        else:
            print("❌ 程序执行失败！")
            print("⚠️ 请检查:")
            print("   1. 微信是否正在运行")
            print("   2. 微信窗口是否可见")
            print("   3. 是否在正确的界面")
            print("   4. 查看日志文件获取详细错误信息")
        
        print("="*60)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
        return 1
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    # 等待用户按键（在某些环境中保持窗口打开）
    if exit_code == 0:
        input("\n按回车键退出...")
    
    sys.exit(exit_code)
