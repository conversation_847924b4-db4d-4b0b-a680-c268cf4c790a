#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试"未刷"检测功能
分析截图并提供详细的调试信息
"""

import cv2
import numpy as np
import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_screenshot_colors(screenshot_path: str):
    """分析截图中的颜色分布"""
    print(f"🎨 分析截图颜色: {screenshot_path}")
    
    try:
        # 读取图像
        image = cv2.imread(screenshot_path)
        if image is None:
            print("❌ 无法读取截图")
            return
        
        height, width = image.shape[:2]
        print(f"📐 图像尺寸: {width}x{height}")
        
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 分析左侧区域（标签列表区域）
        left_region = image[:, :width//2]
        left_hsv = hsv[:, :width//2]
        
        print(f"🔍 分析左侧区域: {width//2}x{height}")
        
        # 检测各种绿色范围
        green_ranges = [
            ("浅绿色", np.array([40, 30, 30]), np.array([80, 255, 255])),
            ("中绿色", np.array([50, 50, 50]), np.array([70, 255, 255])),
            ("深绿色", np.array([60, 100, 100]), np.array([80, 255, 255])),
            ("亮绿色", np.array([40, 100, 150]), np.array([80, 255, 255])),
        ]
        
        for name, lower, upper in green_ranges:
            mask = cv2.inRange(left_hsv, lower, upper)
            green_pixels = cv2.countNonZero(mask)
            percentage = (green_pixels / (left_region.shape[0] * left_region.shape[1])) * 100
            
            print(f"  {name}: {green_pixels} 像素 ({percentage:.2f}%)")
            
            if green_pixels > 100:  # 如果有足够的绿色像素
                # 查找绿色区域的轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                print(f"    找到 {len(contours)} 个绿色区域:")
                for i, contour in enumerate(contours[:5]):  # 只显示前5个
                    x, y, w, h = cv2.boundingRect(contour)
                    area = w * h
                    if area > 50:  # 过滤太小的区域
                        print(f"      区域 {i+1}: 位置({x},{y}), 大小({w}x{h}), 面积={area}")
                        
                        # 保存这个区域的调试图像
                        debug_image = left_region.copy()
                        cv2.rectangle(debug_image, (x, y), (x+w, y+h), (0, 255, 0), 2)
                        cv2.putText(debug_image, f"{name}_{i+1}", (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                        
                        debug_path = screenshot_path.replace('.png', f'_debug_{name}_{i+1}.png')
                        cv2.imwrite(debug_path, debug_image)
                        print(f"        调试图像: {debug_path}")
        
        # 分析文字区域
        print("\n📝 分析文字区域:")
        gray = cv2.cvtColor(left_region, cv2.COLOR_BGR2GRAY)
        
        # 使用多种阈值检测文字
        thresholds = [
            ("OTSU", cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]),
            ("固定阈值127", cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)[1]),
            ("自适应阈值", cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)),
        ]
        
        for name, binary in thresholds:
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            text_regions = 0
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                aspect_ratio = w / h if h > 0 else 0
                
                # 文字区域特征
                if (15 <= w <= 100 and 10 <= h <= 40 and 
                    1.0 <= aspect_ratio <= 6.0 and area >= 100):
                    text_regions += 1
            
            print(f"  {name}: 检测到 {text_regions} 个文字区域")
        
        # 保存左侧区域的调试图像
        left_debug_path = screenshot_path.replace('.png', '_left_region_debug.png')
        cv2.imwrite(left_debug_path, left_region)
        print(f"\n💾 左侧区域调试图像: {left_debug_path}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def manual_click_test(screenshot_path: str):
    """手动点击测试"""
    print(f"\n🖱️ 手动点击测试")
    
    try:
        # 读取图像
        image = cv2.imread(screenshot_path)
        if image is None:
            print("❌ 无法读取截图")
            return
        
        height, width = image.shape[:2]
        
        print("请根据图片中'未刷'文字的位置，输入大概的坐标:")
        print(f"图像尺寸: {width}x{height}")
        print("提示: 根据您提供的图片，'未刷'大概在左侧列表中")
        
        try:
            x_input = input("请输入X坐标 (0-350): ").strip()
            y_input = input("请输入Y坐标 (0-1032): ").strip()
            
            if x_input and y_input:
                x = int(x_input)
                y = int(y_input)
                
                if 0 <= x <= width and 0 <= y <= height:
                    # 在图像上标记这个点
                    test_image = image.copy()
                    cv2.circle(test_image, (x, y), 10, (0, 0, 255), 2)
                    cv2.putText(test_image, f"Click({x},{y})", (x+15, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                    
                    test_path = screenshot_path.replace('.png', '_manual_click_test.png')
                    cv2.imwrite(test_path, test_image)
                    print(f"✅ 测试点标记图像: {test_path}")
                    
                    # 分析这个点周围的区域
                    region_size = 50
                    x1 = max(0, x - region_size//2)
                    y1 = max(0, y - region_size//2)
                    x2 = min(width, x + region_size//2)
                    y2 = min(height, y + region_size//2)
                    
                    roi = image[y1:y2, x1:x2]
                    if roi.size > 0:
                        # 分析这个区域的颜色
                        hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
                        mean_hsv = np.mean(hsv_roi, axis=(0, 1))
                        
                        print(f"📊 点击区域分析:")
                        print(f"   区域: ({x1},{y1}) 到 ({x2},{y2})")
                        print(f"   平均HSV: H={mean_hsv[0]:.1f}, S={mean_hsv[1]:.1f}, V={mean_hsv[2]:.1f}")
                        
                        # 保存这个区域
                        roi_path = screenshot_path.replace('.png', '_click_roi.png')
                        cv2.imwrite(roi_path, roi)
                        print(f"   区域图像: {roi_path}")
                        
                        return (x, y)
                else:
                    print("❌ 坐标超出范围")
            else:
                print("跳过手动测试")
        except ValueError:
            print("❌ 坐标格式错误")
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🔍 '未刷'检测调试工具")
    print("=" * 40)
    
    # 查找最新的截图
    screenshots_dir = "screenshots"
    if not os.path.exists(screenshots_dir):
        print("❌ 截图目录不存在")
        return
    
    import glob
    screenshot_files = glob.glob(os.path.join(screenshots_dir, "wechat_window_*.png"))
    if not screenshot_files:
        print("❌ 没有找到截图文件")
        return
    
    # 使用最新的截图
    latest_screenshot = max(screenshot_files, key=os.path.getmtime)
    print(f"📸 使用最新截图: {latest_screenshot}")
    
    # 分析截图颜色
    analyze_screenshot_colors(latest_screenshot)
    
    # 手动点击测试
    click_pos = manual_click_test(latest_screenshot)
    
    if click_pos:
        print(f"\n🎯 建议的点击坐标: {click_pos}")
        print("可以使用这个坐标来测试实际点击效果")
    
    print("\n💡 调试建议:")
    print("1. 查看生成的调试图像，了解颜色检测结果")
    print("2. 根据分析结果调整检测参数")
    print("3. 使用手动坐标进行点击测试")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
