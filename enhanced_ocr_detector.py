#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的OCR文字检测模块
专门用于识别微信界面中的"未刷"文字标签
"""

import cv2
import numpy as np
import os
import logging
from typing import List, Dict, Optional, Tuple, Union
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import re

logger = logging.getLogger(__name__)

class EnhancedOCRDetector:
    """增强的OCR文字检测器"""
    
    def __init__(self):
        """初始化OCR检测器"""
        self.target_keywords = ["未刷", "未读", "unread", "unswiped"]
        self.ocr_config = {
            'chi_sim': '--oem 1 --psm 8 -c tessedit_char_whitelist=未刷未读',
            'eng': '--oem 1 --psm 8 -c tessedit_char_whitelist=unreadunswiped',
            'mixed': '--oem 1 --psm 6 -l chi_sim+eng'
        }
        
        # 检查tesseract是否可用
        self.ocr_available = self._check_ocr_availability()
        
        logger.info(f"🔤 OCR检测器初始化完成，OCR可用: {self.ocr_available}")
    
    def _check_ocr_availability(self) -> bool:
        """检查OCR是否可用"""
        try:
            # 创建一个简单的测试图像
            test_image = np.ones((50, 100, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "test", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            # 尝试OCR识别
            result = pytesseract.image_to_string(test_image, config='--psm 8')
            return True
        except Exception as e:
            logger.warning(f"OCR不可用: {e}")
            return False
    
    def detect_unswiped_text(self, image: np.ndarray, region_info: Dict = None) -> List[Dict]:
        """
        检测图像中的"未刷"文字
        
        Args:
            image: 输入图像
            region_info: 区域信息（用于坐标转换）
            
        Returns:
            检测到的文字候选列表
        """
        if not self.ocr_available:
            logger.warning("OCR不可用，跳过文字检测")
            return []
        
        try:
            logger.info("🔍 开始OCR文字检测")
            
            candidates = []
            
            # 方法1: 直接OCR检测
            direct_candidates = self._direct_ocr_detection(image)
            candidates.extend(direct_candidates)
            logger.info(f"直接OCR检测: {len(direct_candidates)} 个候选")
            
            # 方法2: 预处理后OCR检测
            processed_candidates = self._preprocessed_ocr_detection(image)
            candidates.extend(processed_candidates)
            logger.info(f"预处理OCR检测: {len(processed_candidates)} 个候选")
            
            # 方法3: 多尺度OCR检测
            multiscale_candidates = self._multiscale_ocr_detection(image)
            candidates.extend(multiscale_candidates)
            logger.info(f"多尺度OCR检测: {len(multiscale_candidates)} 个候选")
            
            # 去重和排序
            final_candidates = self._filter_and_rank_candidates(candidates)
            
            logger.info(f"✅ OCR检测完成，最终候选: {len(final_candidates)} 个")
            return final_candidates
            
        except Exception as e:
            logger.error(f"OCR文字检测失败: {e}")
            return []
    
    def _direct_ocr_detection(self, image: np.ndarray) -> List[Dict]:
        """直接OCR检测"""
        try:
            candidates = []
            
            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # 尝试不同的OCR配置
            for config_name, config in self.ocr_config.items():
                try:
                    # 获取详细的OCR结果
                    data = pytesseract.image_to_data(
                        pil_image, 
                        config=config, 
                        output_type=pytesseract.Output.DICT
                    )
                    
                    # 分析OCR结果
                    for i in range(len(data['text'])):
                        text = data['text'][i].strip()
                        confidence = int(data['conf'][i]) if data['conf'][i] != '-1' else 0
                        
                        if confidence > 30 and text:
                            # 检查是否包含目标关键词
                            if self._is_target_text(text):
                                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                                
                                if w > 15 and h > 10:  # 最小尺寸过滤
                                    candidate = {
                                        'rect': (x, y, w, h),
                                        'center': (x + w//2, y + h//2),
                                        'confidence': min(0.95, confidence / 100.0 + 0.3),
                                        'detection_method': f'direct_ocr_{config_name}',
                                        'type': 'unswiped_text',
                                        'recognized_text': text,
                                        'ocr_confidence': confidence
                                    }
                                    candidates.append(candidate)
                                    logger.info(f"直接OCR识别: '{text}' (置信度: {confidence})")
                
                except Exception as config_error:
                    logger.debug(f"OCR配置 {config_name} 失败: {config_error}")
                    continue
            
            return candidates
            
        except Exception as e:
            logger.error(f"直接OCR检测失败: {e}")
            return []
    
    def _preprocessed_ocr_detection(self, image: np.ndarray) -> List[Dict]:
        """预处理后OCR检测"""
        try:
            candidates = []
            
            # 多种预处理方法
            preprocessing_methods = [
                ("高斯去噪", self._gaussian_denoise),
                ("对比度增强", self._enhance_contrast),
                ("锐化处理", self._sharpen_image),
                ("二值化", self._binarize_image),
                ("形态学处理", self._morphological_process)
            ]
            
            for method_name, preprocess_func in preprocessing_methods:
                try:
                    processed_image = preprocess_func(image)
                    
                    # 对预处理后的图像进行OCR
                    pil_image = Image.fromarray(cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB))
                    
                    # 使用混合语言配置
                    data = pytesseract.image_to_data(
                        pil_image,
                        config=self.ocr_config['mixed'],
                        output_type=pytesseract.Output.DICT
                    )
                    
                    # 分析结果
                    for i in range(len(data['text'])):
                        text = data['text'][i].strip()
                        confidence = int(data['conf'][i]) if data['conf'][i] != '-1' else 0
                        
                        if confidence > 25 and text and self._is_target_text(text):
                            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                            
                            if w > 15 and h > 10:
                                candidate = {
                                    'rect': (x, y, w, h),
                                    'center': (x + w//2, y + h//2),
                                    'confidence': min(0.9, confidence / 100.0 + 0.2),
                                    'detection_method': f'preprocessed_ocr_{method_name}',
                                    'type': 'unswiped_text',
                                    'recognized_text': text,
                                    'ocr_confidence': confidence,
                                    'preprocessing': method_name
                                }
                                candidates.append(candidate)
                                logger.info(f"预处理OCR识别 ({method_name}): '{text}' (置信度: {confidence})")
                
                except Exception as method_error:
                    logger.debug(f"预处理方法 {method_name} 失败: {method_error}")
                    continue
            
            return candidates
            
        except Exception as e:
            logger.error(f"预处理OCR检测失败: {e}")
            return []
    
    def _multiscale_ocr_detection(self, image: np.ndarray) -> List[Dict]:
        """多尺度OCR检测"""
        try:
            candidates = []
            
            # 不同的缩放比例
            scales = [1.0, 1.5, 2.0, 2.5]
            
            for scale in scales:
                try:
                    if scale != 1.0:
                        # 缩放图像
                        height, width = image.shape[:2]
                        new_width = int(width * scale)
                        new_height = int(height * scale)
                        scaled_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                    else:
                        scaled_image = image.copy()
                    
                    # 对缩放后的图像进行OCR
                    pil_image = Image.fromarray(cv2.cvtColor(scaled_image, cv2.COLOR_BGR2RGB))
                    
                    data = pytesseract.image_to_data(
                        pil_image,
                        config=self.ocr_config['mixed'],
                        output_type=pytesseract.Output.DICT
                    )
                    
                    # 分析结果并调整坐标
                    for i in range(len(data['text'])):
                        text = data['text'][i].strip()
                        confidence = int(data['conf'][i]) if data['conf'][i] != '-1' else 0
                        
                        if confidence > 20 and text and self._is_target_text(text):
                            # 调整坐标到原始图像尺寸
                            x = int(data['left'][i] / scale)
                            y = int(data['top'][i] / scale)
                            w = int(data['width'][i] / scale)
                            h = int(data['height'][i] / scale)
                            
                            if w > 10 and h > 8:
                                candidate = {
                                    'rect': (x, y, w, h),
                                    'center': (x + w//2, y + h//2),
                                    'confidence': min(0.85, confidence / 100.0 + 0.15),
                                    'detection_method': f'multiscale_ocr_{scale}x',
                                    'type': 'unswiped_text',
                                    'recognized_text': text,
                                    'ocr_confidence': confidence,
                                    'scale': scale
                                }
                                candidates.append(candidate)
                                logger.info(f"多尺度OCR识别 ({scale}x): '{text}' (置信度: {confidence})")
                
                except Exception as scale_error:
                    logger.debug(f"缩放 {scale}x OCR失败: {scale_error}")
                    continue
            
            return candidates
            
        except Exception as e:
            logger.error(f"多尺度OCR检测失败: {e}")
            return []
    
    def _is_target_text(self, text: str) -> bool:
        """检查文本是否为目标关键词"""
        text_lower = text.lower().strip()
        
        # 直接匹配
        for keyword in self.target_keywords:
            if keyword in text_lower:
                return True
        
        # 模糊匹配（处理OCR识别错误）
        fuzzy_patterns = [
            r'未.*刷',  # 未X刷
            r'.*未刷.*',  # X未刷X
            r'unread',
            r'unswiped'
        ]
        
        for pattern in fuzzy_patterns:
            if re.search(pattern, text_lower):
                return True
        
        return False
    
    def _gaussian_denoise(self, image: np.ndarray) -> np.ndarray:
        """高斯去噪"""
        return cv2.GaussianBlur(image, (3, 3), 0)
    
    def _enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """增强对比度"""
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        enhancer = ImageEnhance.Contrast(pil_image)
        enhanced = enhancer.enhance(1.5)
        return cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
    
    def _sharpen_image(self, image: np.ndarray) -> np.ndarray:
        """锐化图像"""
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        return cv2.filter2D(image, -1, kernel)
    
    def _binarize_image(self, image: np.ndarray) -> np.ndarray:
        """二值化图像"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    
    def _morphological_process(self, image: np.ndarray) -> np.ndarray:
        """形态学处理"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        return cv2.cvtColor(processed, cv2.COLOR_GRAY2BGR)
    
    def _filter_and_rank_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """过滤和排序候选项"""
        if not candidates:
            return []
        
        # 去重（基于位置相似性）
        unique_candidates = []
        for candidate in candidates:
            center = candidate['center']
            is_duplicate = False
            
            for existing in unique_candidates:
                existing_center = existing['center']
                distance = np.sqrt((center[0] - existing_center[0])**2 + (center[1] - existing_center[1])**2)
                
                if distance < 30:  # 距离小于30像素认为是重复
                    if candidate['confidence'] > existing['confidence']:
                        unique_candidates.remove(existing)
                        break
                    else:
                        is_duplicate = True
                        break
            
            if not is_duplicate:
                unique_candidates.append(candidate)
        
        # 按置信度排序
        unique_candidates.sort(key=lambda x: x['confidence'], reverse=True)
        
        return unique_candidates

# 创建全局实例
enhanced_ocr_detector = EnhancedOCRDetector()

def get_enhanced_ocr_detector():
    """获取增强OCR检测器实例"""
    return enhanced_ocr_detector
