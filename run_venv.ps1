# 微信通讯录自动化脚本启动器 (PowerShell版)
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "微信通讯录自动化脚本启动器 (PowerShell版)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查虚拟环境
Write-Host "检查虚拟环境..." -ForegroundColor Yellow
if (-not (Test-Path ".venv\Scripts\python.exe")) {
    Write-Host "❌ 虚拟环境不存在，正在创建..." -ForegroundColor Red
    
    try {
        python -m venv .venv
        Write-Host "✅ 虚拟环境创建成功" -ForegroundColor Green
        
        Write-Host "安装依赖包..." -ForegroundColor Yellow
        & ".venv\Scripts\pip.exe" install -r requirements.txt
        Write-Host "✅ 依赖包安装完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 创建虚拟环境或安装依赖失败: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "✅ 虚拟环境已存在" -ForegroundColor Green
}

Write-Host ""

# 检查微信进程
Write-Host "检查微信是否运行..." -ForegroundColor Yellow
$wechatProcess = Get-Process -Name "WeChat" -ErrorAction SilentlyContinue
if ($wechatProcess) {
    Write-Host "✅ 微信正在运行" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告：未检测到微信进程" -ForegroundColor Yellow
    Write-Host "请确保微信已启动并登录" -ForegroundColor Yellow
    Write-Host ""
    
    $choice = Read-Host "是否继续运行脚本？(y/n)"
    if ($choice -ne "y" -and $choice -ne "Y") {
        Write-Host "脚本已取消" -ForegroundColor Yellow
        Read-Host "按任意键退出"
        exit 0
    }
}

Write-Host ""
Write-Host "启动自动化脚本..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    # 使用虚拟环境中的Python运行脚本
    & ".venv\Scripts\python.exe" "wechat_contacts_automation.py"
}
catch {
    Write-Host "❌ 脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "脚本执行完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "按任意键退出"
